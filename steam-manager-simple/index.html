<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steam账号管理工具 - 专业的多账号管理解决方案</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .main-content {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .section {
            padding: 40px;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .feature-card .icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .feature-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .download-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .download-section h2 {
            color: white;
            border-bottom: 3px solid white;
        }

        .download-buttons {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .download-btn {
            background: white;
            color: #667eea;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            min-width: 200px;
            justify-content: center;
        }

        .download-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            background: #f8f9fa;
        }

        .tech-stack {
            background: #f8f9fa;
        }

        .tech-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .tech-item {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 500;
        }

        .requirements {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .requirements h4 {
            color: #856404;
            margin-bottom: 15px;
        }

        .requirements ul {
            list-style: none;
            padding-left: 0;
        }

        .requirements li {
            color: #856404;
            margin-bottom: 8px;
            padding-left: 25px;
            position: relative;
        }

        .requirements li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        .footer {
            text-align: center;
            color: white;
            padding: 20px;
            opacity: 0.8;
        }

        .version-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .version-info strong {
            color: #1976d2;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 20px;
            }
            
            .download-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Steam账号管理工具</h1>
            <p>专业的Steam多账号管理解决方案，支持自动登录、封禁检测、SteamID管理和安妮程序集成</p>
        </div>

        <div class="main-content">
            <!-- 功能概述 -->
            <div class="section">
                <h2>🚀 功能特性</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="icon">👥</div>
                        <h4>多账号管理</h4>
                        <p>支持无限数量账号存储，批量导入导出，智能分页显示，账号状态实时监控</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon">🔑</div>
                        <h4>一键登录</h4>
                        <p>快速切换Steam账号，自动启动游戏，防重复登录，支持自动关闭当前游戏</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon">🛡️</div>
                        <h4>封禁检测</h4>
                        <p>自动检测VAC和游戏封禁状态，实时监控账号安全，支持批量检测和定时检测</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon">🆔</div>
                        <h4>SteamID管理</h4>
                        <p>自动获取和管理SteamID，支持后台批量更新，用于封禁检测和API调用</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon">🤖</div>
                        <h4>安妮程序集成</h4>
                        <p>智能启动安妮程序，自动处理弹窗，完成后自动启动游戏，支持超时检测</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon">💾</div>
                        <h4>配置持久化</h4>
                        <p>所有设置自动保存到本地，重启程序后配置保持不变，支持手动配置编辑</p>
                    </div>
                </div>
            </div>

            <!-- 技术栈 -->
            <div class="section tech-stack">
                <h2>🛠️ 技术栈</h2>
                <div class="tech-list">
                    <span class="tech-item">Go 1.23+</span>
                    <span class="tech-item">Wails v2</span>
                    <span class="tech-item">SQLite</span>
                    <span class="tech-item">HTML5/CSS3/JavaScript</span>
                    <span class="tech-item">Steam API</span>
                    <span class="tech-item">Windows API</span>
                </div>
                
                <div class="version-info">
                    <strong>当前版本：</strong> v2.0 - 支持安妮程序集成和配置持久化
                </div>
            </div>

            <!-- 快速开始 -->
            <div class="section">
                <h2>🚀 快速开始</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="icon">1️⃣</div>
                        <h4>下载程序</h4>
                        <p>下载适合的版本，正式版包含完整功能，开发版体积更小</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon">2️⃣</div>
                        <h4>首次启动</h4>
                        <p>双击运行程序，首次启动会自动创建配置文件和数据库</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon">3️⃣</div>
                        <h4>添加账号</h4>
                        <p>点击"添加账号"按钮或批量导入账号文件开始管理</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon">4️⃣</div>
                        <h4>开始使用</h4>
                        <p>选择账号点击登录，享受自动化的Steam账号管理体验</p>
                    </div>
                </div>

                <div class="version-info">
                    <strong>💡 一次配置，永久生效：</strong> 所有设置都会自动保存，下次启动程序时无需重新配置
                </div>
            </div>

            <!-- 系统要求 -->
            <div class="section">
                <h2>📋 系统要求</h2>
                <div class="requirements">
                    <h4>运行环境要求：</h4>
                    <ul>
                        <li>操作系统：Windows 10/11</li>
                        <li>Steam客户端：已安装并可正常运行</li>
                        <li>网络连接：用于Steam API调用和SteamID获取</li>
                        <li>WinRAR：用于安妮程序解压（如需使用安妮功能）</li>
                        <li>内存：建议2GB以上可用内存</li>
                        <li>存储：至少50MB可用磁盘空间</li>
                    </ul>
                </div>
            </div>

            <!-- 下载区域 -->
            <div class="section download-section">
                <h2>📥 立即下载</h2>
                <p>选择适合您的版本，开始体验专业的Steam账号管理</p>
                <div class="download-buttons">
                    <a href="./build/bin/steam-manager-simple.exe" class="download-btn" download>
                        💻 下载正式版本
                    </a>
                    <a href="./steam-manager-simple.exe" class="download-btn" download>
                        🔧 下载开发版本
                    </a>
                    <a href="./使用说明-an.html" class="download-btn" target="_blank">
                        📖 使用说明
                    </a>
                    <a href="https://github.com/Dk2014/steam-acc-mgr" class="download-btn" target="_blank">
                        📂 查看源代码
                    </a>
                </div>
                <p style="margin-top: 20px; opacity: 0.9;">
                    正式版大小：约 25MB | 开发版大小：约 15MB | 支持 Windows 10/11 | 免费使用
                </p>
            </div>
        </div>

        <!-- 开发者工具 -->
        <div class="main-content" style="margin-bottom: 20px;">
            <div class="section">
                <h2>🛠️ 开发者工具</h2>
                <p>如果您想自己构建或开发此项目，可以使用以下脚本：</p>
                <div class="download-buttons">
                    <a href="./deploy.bat" class="download-btn" download>
                        🔨 一键部署脚本
                    </a>
                    <a href="./dev.bat" class="download-btn" download>
                        🚀 开发模式脚本
                    </a>
                    <a href="./start.bat" class="download-btn" download>
                        ▶️ 快速启动脚本
                    </a>
                </div>
                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px; font-size: 0.9rem; color: #666;">
                    <strong>脚本说明：</strong><br>
                    • <strong>deploy.bat</strong> - 自动检查环境、安装依赖并构建项目<br>
                    • <strong>dev.bat</strong> - 启动开发服务器，支持热重载<br>
                    • <strong>start.bat</strong> - 快速启动已构建的程序
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 Steam账号管理工具 | 基于 Go + Wails 开发 | 开源免费</p>
            <p style="margin-top: 10px; font-size: 0.9rem;">
                特别适用于 PUBG 等 Steam 游戏的多账号管理需求
            </p>
        </div>
    </div>

    <script>
        // 简单的自定义提示框函数
        function showSimpleAlert(message, type = 'info', title = '提示') {
            // 创建模态框HTML
            const icons = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌'
            };

            const modalHTML = `
                <div id="simpleModal" style="
                    display: block;
                    position: fixed;
                    z-index: 2000;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.6);
                    animation: fadeIn 0.3s ease-out;
                ">
                    <div style="
                        background-color: white;
                        margin: 15% auto;
                        padding: 0;
                        border-radius: 12px;
                        width: 90%;
                        max-width: 400px;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                        animation: slideIn 0.3s ease-out;
                        overflow: hidden;
                    ">
                        <div style="
                            display: flex;
                            align-items: center;
                            padding: 20px 24px 16px;
                            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
                            color: white;
                        ">
                            <div style="font-size: 24px; margin-right: 12px;">${icons[type] || icons.info}</div>
                            <div style="font-size: 18px; font-weight: 600;">${title}</div>
                        </div>
                        <div style="
                            padding: 24px;
                            font-size: 16px;
                            line-height: 1.5;
                            color: #2c3e50;
                            text-align: center;
                        ">${message}</div>
                        <div style="
                            display: flex;
                            justify-content: flex-end;
                            padding: 16px 24px 24px;
                            background: #f8f9fa;
                        ">
                            <button onclick="hideSimpleModal()" style="
                                padding: 10px 20px;
                                border: none;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                                font-weight: 600;
                                background: #3498db;
                                color: white;
                                transition: all 0.2s ease;
                            ">确定</button>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('simpleModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function hideSimpleModal() {
            const modal = document.getElementById('simpleModal');
            if (modal) {
                modal.remove();
            }
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为功能卡片添加点击效果
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 检查下载文件是否存在
            const downloadBtns = document.querySelectorAll('.download-btn[download]');
            downloadBtns.forEach((btn, index) => {
                const url = btn.getAttribute('href');
                fetch(url, { method: 'HEAD' })
                    .then(response => {
                        if (!response.ok) {
                            btn.innerHTML = index === 0 ? '🔧 构建中...' : '🔧 开发版构建中...';
                            btn.style.opacity = '0.6';
                            btn.style.cursor = 'not-allowed';
                            btn.onclick = function(e) {
                                e.preventDefault();
                                showSimpleAlert('程序正在构建中，请稍后再试或查看源代码自行编译。', 'warning', '构建提示');
                            };
                        }
                    })
                    .catch(() => {
                        // 文件不存在时的处理
                        btn.innerHTML = index === 0 ? '🔧 需要构建' : '🔧 开发版需要构建';
                        btn.style.opacity = '0.6';
                    });
            });
        });
    </script>
</body>
</html>
