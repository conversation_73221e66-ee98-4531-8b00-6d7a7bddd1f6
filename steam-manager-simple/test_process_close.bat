@echo off
chcp 65001 >nul
title 测试进程关闭功能

echo ========================================
echo 测试进程关闭功能
echo ========================================
echo.

echo 🔍 当前运行的游戏相关进程:
echo.

:: 检查常见游戏进程
echo 检查PUBG进程:
tasklist /FI "IMAGENAME eq TslGame.exe" 2>nul | findstr /C:"TslGame.exe" && echo ✅ 发现 TslGame.exe || echo ❌ 未发现 TslGame.exe
tasklist /FI "IMAGENAME eq TslGame_BE.exe" 2>nul | findstr /C:"TslGame_BE.exe" && echo ✅ 发现 TslGame_BE.exe || echo ❌ 未发现 TslGame_BE.exe

echo.
echo 检查NARAKA进程:
tasklist /FI "IMAGENAME eq StartGame.exe" 2>nul | findstr /C:"StartGame.exe" && echo ✅ 发现 StartGame.exe || echo ❌ 未发现 StartGame.exe
tasklist /FI "IMAGENAME eq UnityCrashHandler64.exe" 2>nul | findstr /C:"UnityCrashHandler64.exe" && echo ✅ 发现 UnityCrashHandler64.exe || echo ❌ 未发现 UnityCrashHandler64.exe

echo.
echo 检查其他常见游戏进程:
tasklist /FI "IMAGENAME eq csgo.exe" 2>nul | findstr /C:"csgo.exe" && echo ✅ 发现 csgo.exe || echo ❌ 未发现 csgo.exe
tasklist /FI "IMAGENAME eq dota2.exe" 2>nul | findstr /C:"dota2.exe" && echo ✅ 发现 dota2.exe || echo ❌ 未发现 dota2.exe

echo.
echo ========================================
echo 测试完成
echo ========================================
echo.
echo 💡 提示：
echo - 如果发现了游戏进程，可以测试Steam账号管理工具的关闭功能
echo - 新版本会根据当前选择的游戏智能关闭对应进程
echo - 查看程序日志了解详细的关闭过程
echo.
pause
