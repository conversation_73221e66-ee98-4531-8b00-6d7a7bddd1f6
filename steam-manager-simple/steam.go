package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"syscall"
	"time"
)

// LoginSteam Steam登录
func (a *App) LoginSteam(username, password string) error {
	// 简单验证
	if username == "" || password == "" {
		return fmt.Errorf("用户名和密码不能为空")
	}

	// 启动Steam登录
	err := a.launchSteamWithAccount(username, password)
	if err != nil {
		return err
	}

	// 更新账号登录状态
	go func() {
		// 等待Steam启动完成
		time.Sleep(3 * time.Second)
		a.updateAccountLastLogin(username)
	}()

	return nil
}

// launchSteamWithAccount 启动Steam并登录指定账号
func (a *App) launchSteamWithAccount(username, password string) error {
	// 如果配置了自动关闭游戏，先关闭游戏
	if a.config.Steam.GameConfig.AutoCloseGame {
		a.addLog("INFO", "正在关闭现有游戏进程...", "general")
		if err := a.closeCurrentGame(); err != nil {
			a.addLog("WARN", fmt.Sprintf("关闭游戏失败: %v", err), "general")
		}
	}

	// 检查Steam是否正在运行
	if a.isSteamRunning() {
		// 如果Steam正在运行，先关闭它
		a.addLog("INFO", "正在关闭现有Steam进程...", "general")
		if err := a.closeSteam(); err != nil {
			a.addLog("WARN", fmt.Sprintf("关闭Steam失败: %v", err), "general")
		}
		// 等待Steam完全关闭
		time.Sleep(3 * time.Second)
	}

	// 启动Steam并登录
	return a.startSteamWithLogin(username, password)
}

// isSteamRunning 检查Steam是否正在运行
func (a *App) isSteamRunning() bool {
	// 使用tasklist命令检查Steam进程
	result, err := a.executeCommand("tasklist", "/FI", "IMAGENAME eq Steam.exe")
	if err != nil {
		return false
	}

	// 检查输出中是否包含Steam.exe
	return strings.Contains(strings.ToLower(result), "steam.exe")
}

// closeSteam 关闭Steam
func (a *App) closeSteam() error {
	// 使用taskkill命令关闭Steam
	cmd := `taskkill /F /IM Steam.exe`
	_, err := a.executeCommand("cmd", "/C", cmd)
	return err
}

// isGameRunning 检查指定游戏进程是否正在运行
func (a *App) isGameRunning(processName string) bool {
	result, err := a.executeCommand("tasklist", "/FI", fmt.Sprintf("IMAGENAME eq %s", processName))
	if err != nil {
		return false
	}
	// 检查输出中是否包含进程名
	return strings.Contains(strings.ToLower(result), strings.ToLower(processName))
}

// isPUBGRunning 检查PUBG是否正在运行
func (a *App) isPUBGRunning() bool {
	// PUBG的可能进程名
	pubgProcesses := []string{
		"TslGame.exe",
		"TslGame_BE.exe",
		"PUBG.exe",
		"ExecPubg.exe",
	}

	for _, processName := range pubgProcesses {
		if a.isGameRunning(processName) {
			return true
		}
	}
	return false
}

// closeGame 关闭指定的游戏进程
func (a *App) closeGame(processName string) error {
	if !a.isGameRunning(processName) {
		return nil // 游戏没有运行，无需关闭
	}

	// 尝试优雅关闭
	cmd := fmt.Sprintf(`taskkill /IM %s`, processName)
	_, err := a.executeCommand("cmd", "/C", cmd)
	if err == nil {
		// 等待进程关闭
		time.Sleep(3 * time.Second)
		if !a.isGameRunning(processName) {
			return nil
		}
	}

	// 强制关闭
	cmd = fmt.Sprintf(`taskkill /F /IM %s`, processName)
	_, err = a.executeCommand("cmd", "/C", cmd)
	return err
}

// closePUBG 关闭PUBG游戏
func (a *App) closePUBG() error {
	// PUBG的可能进程名
	pubgProcesses := []string{
		"TslGame.exe",
		"TslGame_BE.exe",
		"PUBG.exe",
		"ExecPubg.exe",
	}

	var lastErr error
	for _, processName := range pubgProcesses {
		err := a.closeGame(processName)
		if err != nil {
			lastErr = err
			a.addLog("ERROR", fmt.Sprintf("关闭 %s 失败: %v", processName, err), "general")
		} else if a.isGameRunning(processName) {
			a.addLog("SUCCESS", fmt.Sprintf("成功关闭 %s", processName), "general")
		}
	}

	return lastErr
}

// startSteamWithLogin 启动Steam并登录
func (a *App) startSteamWithLogin(username, password string) error {
	// 查找Steam安装路径
	steamPath := a.findSteamPath()
	if steamPath == "" {
		return fmt.Errorf("未找到Steam安装路径")
	}

	// 构建Steam启动命令
	steamExe := filepath.Join(steamPath, "Steam.exe")

	// 启动Steam（不等待完成，隐藏窗口）
	cmd := exec.Command(steamExe, "-login", username, password)

	// 在Windows上隐藏命令行窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	err := cmd.Start()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("启动Steam失败: %v", err), "general")
		return fmt.Errorf("启动Steam失败: %v", err)
	}

	a.addLog("SUCCESS", fmt.Sprintf("Steam启动命令已执行: %s -login %s ***", steamExe, username), "general")

	// 等待Steam启动
	time.Sleep(5 * time.Second)

	// 如果配置了自动启动游戏，则启动游戏
	if a.config.Steam.AutoLaunchGame {
		go func() {
			// 等待Steam完全启动
			time.Sleep(10 * time.Second)

			// 如果启用了安妮程序，先启动安妮程序
			annieSuccess := false
			if a.config.Annie.Enabled {
				a.addLog("INFO", "Steam登录成功，开始启动安妮程序", "general")
				if err := a.launchAnnie(); err != nil {
					a.addLog("ERROR", fmt.Sprintf("启动安妮程序失败: %v", err), "general")
					a.addLog("INFO", "安妮程序启动失败，跳过游戏启动", "general")
					return // 安妮程序启动失败，不启动游戏
				} else {
					// 安妮程序启动成功，等待其处理完成后再启动游戏
					a.addLog("INFO", "安妮程序启动成功，等待处理完成后启动游戏", "general")
					// 等待并处理弹窗、进程退出和文件消失
					if err := a.handleAnnieDialogs(); err != nil {
						a.addLog("ERROR", fmt.Sprintf("安妮程序处理失败: %v", err), "general")
						a.addLog("INFO", "安妮程序处理失败，跳过游戏启动", "general")
						return // 安妮程序处理失败，不启动游戏
					}
					annieSuccess = true
				}
			}

			// 只有在安妮程序未启用或处理完成后才启动游戏
			if !a.config.Annie.Enabled || annieSuccess {
				if err := a.launchGame(); err != nil {
					a.addLog("ERROR", fmt.Sprintf("启动游戏失败: %v", err), "general")
				}
			}
		}()
	}

	return nil
}

// findSteamPath 查找Steam安装路径
func (a *App) findSteamPath() string {
	// 常见的Steam安装路径
	commonPaths := []string{
		`C:\Program Files (x86)\Steam`,
		`C:\Program Files\Steam`,
		`D:\Steam`,
		`E:\Steam`,
		`F:\Steam`,
	}

	// 检查常见路径
	for _, path := range commonPaths {
		steamExe := filepath.Join(path, "Steam.exe")
		if _, err := os.Stat(steamExe); err == nil {
			return path
		}
	}

	// 尝试从注册表读取
	steamPath := a.getSteamPathFromRegistry()
	if steamPath != "" {
		return steamPath
	}

	return ""
}

// getSteamPathFromRegistry 从注册表获取Steam路径
func (a *App) getSteamPathFromRegistry() string {
	// 使用reg命令查询注册表
	cmd := `reg query "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Valve\Steam" /v InstallPath 2>NUL`
	result, err := a.executeCommand("cmd", "/C", cmd)
	if err != nil {
		return ""
	}

	// 解析注册表输出
	lines := strings.Split(result, "\n")
	for _, line := range lines {
		if strings.Contains(line, "InstallPath") && strings.Contains(line, "REG_SZ") {
			parts := strings.Fields(line)
			if len(parts) >= 3 {
				return strings.Join(parts[2:], " ")
			}
		}
	}

	return ""
}

// launchGame 启动游戏
func (a *App) launchGame() error {
	gameID := a.config.Steam.GameConfig.GameID
	if gameID == "" {
		gameID = "578080" // PUBG的Steam ID
	}

	// 使用Steam URL协议启动游戏
	steamURL := fmt.Sprintf("steam://rungameid/%s", gameID)

	// 使用start命令启动URL（隐藏窗口）
	cmd := exec.Command("cmd", "/C", "start", steamURL)

	// 在Windows上隐藏命令行窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	err := cmd.Start()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("启动游戏失败: %v", err), "general")
		return err
	}

	a.addLog("SUCCESS", fmt.Sprintf("游戏启动命令已执行: %s", steamURL), "general")
	return nil
}

// LaunchGame 公开的启动游戏方法
func (a *App) LaunchGame() error {
	return a.launchGame()
}

// GetInstalledSteamGames 获取本地已安装的Steam游戏列表
func (a *App) GetInstalledSteamGames() ([]SteamGame, error) {
	steamPath := a.findSteamPath()
	if steamPath == "" {
		a.addLog("ERROR", "未找到Steam安装路径", "general")
		return nil, fmt.Errorf("未找到Steam安装路径")
	}

	a.addLog("INFO", fmt.Sprintf("找到Steam安装路径: %s", steamPath), "general")
	games := []SteamGame{}

	// 添加默认的PUBG游戏
	games = append(games, SteamGame{
		AppID: "578080",
		Name:  "绝地求生",
	})

	// 获取所有Steam库路径
	libraryPaths := a.getSteamLibraryPaths(steamPath)
	a.addLog("INFO", fmt.Sprintf("找到 %d 个Steam库路径", len(libraryPaths)), "general")

	for _, libraryPath := range libraryPaths {
		a.addLog("INFO", fmt.Sprintf("扫描Steam库: %s", libraryPath), "general")
		libraryGames := a.scanSteamLibrary(libraryPath)

		for _, game := range libraryGames {
			// 过滤掉一些非游戏的应用（如Steam工具、DLC等）
			if a.isValidGame(game) {
				games = append(games, game)
				a.addLog("INFO", fmt.Sprintf("添加有效游戏: %s (%s)", game.Name, game.AppID), "general")
			} else {
				a.addLog("INFO", fmt.Sprintf("过滤掉非游戏应用: %s (%s)", game.Name, game.AppID), "general")
			}
		}
	}

	a.addLog("INFO", fmt.Sprintf("最终找到 %d 个已安装的Steam游戏", len(games)), "general")
	return games, nil
}

// parseACFFile 解析ACF文件获取游戏信息
func (a *App) parseACFFile(filePath string) (SteamGame, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return SteamGame{}, err
	}

	contentStr := string(content)
	game := SteamGame{}

	// 使用正则表达式或更简单的字符串解析
	lines := strings.Split(contentStr, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 解析AppID
		if strings.Contains(line, `"appid"`) && strings.Contains(line, `"`) {
			parts := strings.Split(line, `"`)
			if len(parts) >= 4 {
				game.AppID = parts[3]
			}
		}

		// 解析游戏名称
		if strings.Contains(line, `"name"`) && strings.Contains(line, `"`) {
			parts := strings.Split(line, `"`)
			if len(parts) >= 4 {
				game.Name = parts[3]
			}
		}

		// 如果两个都找到了，可以提前退出
		if game.AppID != "" && game.Name != "" {
			break
		}
	}

	if game.AppID == "" || game.Name == "" {
		return SteamGame{}, fmt.Errorf("无法解析游戏信息: AppID=%s, Name=%s", game.AppID, game.Name)
	}

	//// 尝试获取中文名称
	//chineseName := a.getChineseGameName(game.AppID, game.Name)
	//if chineseName != "" {
	//	game.Name = chineseName
	//}

	return game, nil
}

// extractValueFromACF 从ACF内容中提取值
func (a *App) extractValueFromACF(content string) string {
	lines := strings.Split(content, "\n")
	if len(lines) == 0 {
		return ""
	}

	// 找到包含值的行
	firstLine := strings.TrimSpace(lines[0])
	if strings.Contains(firstLine, `"`) {
		// 提取引号中的值
		parts := strings.Split(firstLine, `"`)
		if len(parts) >= 4 {
			return parts[3] // 第二个引号对中的内容
		}
	}

	return ""
}

// isValidGame 判断是否为有效的游戏（过滤工具、DLC等）
func (a *App) isValidGame(game SteamGame) bool {
	// 过滤掉一些明显的非游戏应用
	excludeKeywords := []string{
		"Steamworks Common Redistributables",
		"Steam Client",
		"Proton",
		"DirectX",
		"Visual C++",
		"Microsoft",
	}

	gameName := strings.ToLower(game.Name)
	for _, keyword := range excludeKeywords {
		if strings.Contains(gameName, strings.ToLower(keyword)) {
			a.addLog("INFO", fmt.Sprintf("过滤关键词匹配: %s 包含 %s", game.Name, keyword), "general")
			return false
		}
	}

	// AppID过滤：一些特殊的AppID范围通常不是游戏
	// Steam工具类应用通常AppID较小
	if game.AppID == "228980" || // Steamworks Common Redistributables
		game.AppID == "1007" || // Steam Client Bootstrapper
		game.AppID == "7" { // Steam Client
		a.addLog("INFO", fmt.Sprintf("过滤特殊AppID: %s (%s)", game.Name, game.AppID), "general")
		return false
	}

	// 跳过重复的PUBG（因为我们已经默认添加了）
	if game.AppID == "578080" {
		a.addLog("INFO", fmt.Sprintf("跳过重复的PUBG: %s (%s)", game.Name, game.AppID), "general")
		return false
	}

	return true
}

// getSteamLibraryPaths 获取所有Steam库路径
func (a *App) getSteamLibraryPaths(steamPath string) []string {
	var libraryPaths []string

	// 默认添加主Steam库
	mainLibrary := filepath.Join(steamPath, "steamapps")
	libraryPaths = append(libraryPaths, mainLibrary)

	// 读取libraryfolders.vdf文件获取其他库路径
	vdfPath := filepath.Join(steamPath, "steamapps", "libraryfolders.vdf")
	a.addLog("INFO", fmt.Sprintf("尝试读取库配置文件: %s", vdfPath), "general")

	if _, err := os.Stat(vdfPath); os.IsNotExist(err) {
		a.addLog("WARN", fmt.Sprintf("库配置文件不存在: %s", vdfPath), "general")
		return libraryPaths
	}

	content, err := os.ReadFile(vdfPath)
	if err != nil {
		a.addLog("WARN", fmt.Sprintf("读取库配置文件失败: %v", err), "general")
		return libraryPaths
	}

	// 解析VDF文件获取库路径
	contentStr := string(content)
	lines := strings.Split(contentStr, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		// 查找路径行，格式类似: "path"		"D:\\SteamLibrary"
		if strings.Contains(line, `"path"`) && strings.Count(line, `"`) >= 4 {
			parts := strings.Split(line, `"`)
			if len(parts) >= 4 {
				libPath := parts[3]
				// 处理路径中的双反斜杠
				libPath = strings.ReplaceAll(libPath, `\\`, `\`)
				steamappsPath := filepath.Join(libPath, "steamapps")

				// 检查路径是否存在且不重复
				if _, err := os.Stat(steamappsPath); err == nil {
					// 检查是否已存在
					exists := false
					for _, existing := range libraryPaths {
						if existing == steamappsPath {
							exists = true
							break
						}
					}
					if !exists {
						libraryPaths = append(libraryPaths, steamappsPath)
						a.addLog("INFO", fmt.Sprintf("找到额外Steam库: %s", steamappsPath), "general")
					}
				}
			}
		}
	}

	return libraryPaths
}

// scanSteamLibrary 扫描单个Steam库
func (a *App) scanSteamLibrary(libraryPath string) []SteamGame {
	var games []SteamGame

	// 检查steamapps目录是否存在
	if _, err := os.Stat(libraryPath); os.IsNotExist(err) {
		a.addLog("WARN", fmt.Sprintf("Steam库目录不存在: %s", libraryPath), "general")
		return games
	}

	// 读取所有.acf文件（Steam应用缓存文件）
	acfFiles, err := filepath.Glob(filepath.Join(libraryPath, "appmanifest_*.acf"))
	if err != nil {
		a.addLog("WARN", fmt.Sprintf("读取Steam库游戏列表失败: %s - %v", libraryPath, err), "general")
		return games
	}

	a.addLog("INFO", fmt.Sprintf("在库 %s 中找到 %d 个ACF文件", libraryPath, len(acfFiles)), "general")

	for _, acfFile := range acfFiles {
		a.addLog("INFO", fmt.Sprintf("解析ACF文件: %s", acfFile), "general")
		game, err := a.parseACFFile(acfFile)
		if err != nil {
			a.addLog("WARN", fmt.Sprintf("解析ACF文件失败: %s - %v", acfFile, err), "general")
			continue // 跳过解析失败的文件
		}

		a.addLog("INFO", fmt.Sprintf("解析到游戏: %s (%s)", game.Name, game.AppID), "general")
		games = append(games, game)
	}

	return games
}

// closeCurrentGame 关闭当前选择的游戏
func (a *App) closeCurrentGame() error {
	gameID := a.config.Steam.GameConfig.GameID
	if gameID == "" {
		gameID = "578080" // 默认PUBG
	}

	a.addLog("INFO", fmt.Sprintf("准备关闭游戏 (AppID: %s)", gameID), "general")

	// 根据游戏ID获取游戏安装路径和进程信息
	gameInfo, err := a.getGameInstallInfo(gameID)
	if err != nil {
		a.addLog("WARN", fmt.Sprintf("获取游戏安装信息失败: %v，使用默认进程列表", err), "general")
		// 如果获取失败，使用已知的游戏进程映射
		return a.closeGameByKnownProcesses(gameID)
	}

	// 使用游戏安装路径查找并关闭相关进程
	return a.closeGameByInstallPath(gameInfo)
}

// GameInstallInfo 游戏安装信息
type GameInstallInfo struct {
	AppID       string
	Name        string
	InstallPath string
}

// getGameInstallInfo 获取游戏安装信息
func (a *App) getGameInstallInfo(gameID string) (*GameInstallInfo, error) {
	steamPath := a.findSteamPath()
	if steamPath == "" {
		return nil, fmt.Errorf("未找到Steam安装路径")
	}

	// 获取所有Steam库路径
	libraryPaths := a.getSteamLibraryPaths(steamPath)

	for _, libraryPath := range libraryPaths {
		// 查找对应的ACF文件
		acfFile := filepath.Join(libraryPath, fmt.Sprintf("appmanifest_%s.acf", gameID))
		if _, err := os.Stat(acfFile); os.IsNotExist(err) {
			continue
		}

		// 解析ACF文件获取游戏信息
		game, err := a.parseACFFile(acfFile)
		if err != nil {
			continue
		}

		// 获取游戏安装目录
		installDir, err := a.getGameInstallDir(acfFile)
		if err != nil {
			continue
		}

		installPath := filepath.Join(libraryPath, "common", installDir)
		if _, err := os.Stat(installPath); os.IsNotExist(err) {
			continue
		}

		return &GameInstallInfo{
			AppID:       game.AppID,
			Name:        game.Name,
			InstallPath: installPath,
		}, nil
	}

	return nil, fmt.Errorf("未找到游戏 %s 的安装信息", gameID)
}

// getGameInstallDir 从ACF文件获取游戏安装目录名
func (a *App) getGameInstallDir(acfFile string) (string, error) {
	content, err := os.ReadFile(acfFile)
	if err != nil {
		return "", err
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		// 查找installdir行
		if strings.Contains(line, `"installdir"`) && strings.Contains(line, `"`) {
			parts := strings.Split(line, `"`)
			if len(parts) >= 4 {
				return parts[3], nil
			}
		}
	}

	return "", fmt.Errorf("未找到installdir信息")
}

// closeGameByInstallPath 根据游戏安装路径关闭相关进程
func (a *App) closeGameByInstallPath(gameInfo *GameInstallInfo) error {
	a.addLog("INFO", fmt.Sprintf("开始关闭游戏: %s", gameInfo.Name), "general")

	// 获取所有正在运行的进程
	processes, err := a.getAllRunningProcesses()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("获取进程列表失败: %v", err), "general")
		return err
	}

	var gameProcesses []ProcessInfo
	var closedProcesses []string
	var failedProcesses []string

	// 遍历所有进程，查找与游戏安装路径相关的进程
	for _, process := range processes {
		if a.isGameRelatedProcess(process, gameInfo.InstallPath) {
			gameProcesses = append(gameProcesses, process)
			a.addLog("INFO", fmt.Sprintf("发现游戏相关进程: %s (PID: %s, 路径: %s)", process.Name, process.PID, process.Path), "general")
		}
	}

	if len(gameProcesses) == 0 {
		a.addLog("INFO", "未发现需要关闭的游戏进程", "general")
		return nil
	}

	a.addLog("INFO", fmt.Sprintf("找到 %d 个游戏相关进程，开始关闭", len(gameProcesses)), "general")

	// 关闭所有游戏相关进程
	for _, process := range gameProcesses {
		err := a.closeGameProcess(process.Name)
		if err != nil {
			failedProcesses = append(failedProcesses, process.Name)
			a.addLog("WARN", fmt.Sprintf("关闭进程 %s 失败: %v", process.Name, err), "general")
		} else {
			closedProcesses = append(closedProcesses, process.Name)
		}
	}

	// 等待一下，然后验证关闭结果
	time.Sleep(2 * time.Second)

	// 验证进程是否真的被关闭
	stillRunning := 0
	for _, process := range gameProcesses {
		if a.isGameRunning(process.Name) {
			stillRunning++
			a.addLog("WARN", fmt.Sprintf("进程 %s 仍在运行", process.Name), "general")
		}
	}

	// 报告结果
	if len(closedProcesses) > 0 {
		a.addLog("SUCCESS", fmt.Sprintf("成功关闭 %d 个游戏进程: %v", len(closedProcesses), closedProcesses), "general")
	}

	if len(failedProcesses) > 0 {
		a.addLog("WARN", fmt.Sprintf("关闭失败 %d 个游戏进程: %v", len(failedProcesses), failedProcesses), "general")
	}

	if stillRunning > 0 {
		a.addLog("WARN", fmt.Sprintf("仍有 %d 个游戏进程在运行", stillRunning), "general")
		return fmt.Errorf("部分游戏进程关闭失败")
	}

	a.addLog("SUCCESS", "所有游戏进程已成功关闭", "general")
	return nil
}

// ProcessInfo 进程信息
type ProcessInfo struct {
	Name string
	PID  string
	Path string
}

// getAllRunningProcesses 获取所有正在运行的进程
func (a *App) getAllRunningProcesses() ([]ProcessInfo, error) {
	// 使用wmic命令获取进程信息（包含路径）
	cmd := `wmic process get Name,ProcessId,ExecutablePath /format:csv`
	result, err := a.executeCommand("cmd", "/C", cmd)
	if err != nil {
		return nil, err
	}

	var processes []ProcessInfo
	lines := strings.Split(result, "\n")

	for i, line := range lines {
		// 跳过标题行和空行
		if i < 2 || strings.TrimSpace(line) == "" {
			continue
		}

		// CSV格式解析：Node,ExecutablePath,Name,ProcessId
		parts := strings.Split(line, ",")
		if len(parts) >= 4 {
			name := strings.TrimSpace(parts[2])
			pid := strings.TrimSpace(parts[3])
			path := strings.TrimSpace(parts[1])

			if name != "" && pid != "" {
				processes = append(processes, ProcessInfo{
					Name: name,
					PID:  pid,
					Path: path,
				})
			}
		}
	}

	return processes, nil
}

// isGameRelatedProcess 判断进程是否与游戏相关
func (a *App) isGameRelatedProcess(process ProcessInfo, gameInstallPath string) bool {
	// 如果进程路径为空，跳过
	if process.Path == "" {
		return false
	}

	// 标准化路径（转换为小写，统一路径分隔符）
	processPath := strings.ToLower(strings.ReplaceAll(process.Path, "/", "\\"))
	gamePath := strings.ToLower(strings.ReplaceAll(gameInstallPath, "/", "\\"))

	// 检查进程路径是否在游戏安装目录下（包括子目录）
	isRelated := strings.HasPrefix(processPath, gamePath)

	if isRelated {
		a.addLog("INFO", fmt.Sprintf("进程路径匹配: %s -> %s", processPath, gamePath), "general")
	}

	return isRelated
}

// closeGameProcess 关闭指定的游戏进程
func (a *App) closeGameProcess(processName string) error {
	// 首先检查进程是否真的在运行
	if !a.isGameRunning(processName) {
		a.addLog("INFO", fmt.Sprintf("进程 %s 未运行，无需关闭", processName), "general")
		return nil
	}

	a.addLog("INFO", fmt.Sprintf("尝试关闭进程: %s", processName), "general")

	// 方法1: 尝试优雅关闭
	cmd := fmt.Sprintf(`taskkill /IM "%s"`, processName)
	result, err := a.executeCommand("cmd", "/C", cmd)
	if err == nil {
		a.addLog("SUCCESS", fmt.Sprintf("优雅关闭进程 %s 成功", processName), "general")
		// 等待进程关闭
		time.Sleep(2 * time.Second)
		if !a.isGameRunning(processName) {
			return nil
		}
	} else {
		a.addLog("WARN", fmt.Sprintf("优雅关闭进程 %s 失败: %v, 输出: %s", processName, err, result), "general")
	}

	// 方法2: 强制关闭
	cmd = fmt.Sprintf(`taskkill /F /IM "%s"`, processName)
	result, err = a.executeCommand("cmd", "/C", cmd)
	if err == nil {
		a.addLog("SUCCESS", fmt.Sprintf("强制关闭进程 %s 成功", processName), "general")
		time.Sleep(1 * time.Second)
		if !a.isGameRunning(processName) {
			return nil
		}
	} else {
		a.addLog("WARN", fmt.Sprintf("强制关闭进程 %s 失败: %v, 输出: %s", processName, err, result), "general")
	}

	// 方法3: 使用PID关闭
	return a.closeGameProcessByPID(processName)
}

// closeGameProcessByPID 通过PID关闭进程
func (a *App) closeGameProcessByPID(processName string) error {
	a.addLog("INFO", fmt.Sprintf("尝试通过PID关闭进程: %s", processName), "general")

	// 获取进程PID
	pids, err := a.getProcessPIDs(processName)
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("获取进程 %s 的PID失败: %v", processName, err), "general")
		return err
	}

	if len(pids) == 0 {
		a.addLog("INFO", fmt.Sprintf("进程 %s 已不存在", processName), "general")
		return nil
	}

	var lastErr error
	successCount := 0

	for _, pid := range pids {
		a.addLog("INFO", fmt.Sprintf("尝试关闭进程 %s (PID: %s)", processName, pid), "general")

		// 尝试优雅关闭
		cmd := fmt.Sprintf(`taskkill /PID %s`, pid)
		result, err := a.executeCommand("cmd", "/C", cmd)
		if err == nil {
			a.addLog("SUCCESS", fmt.Sprintf("优雅关闭进程 %s (PID: %s) 成功", processName, pid), "general")
			successCount++
			continue
		}

		a.addLog("WARN", fmt.Sprintf("优雅关闭PID %s 失败: %v, 输出: %s", pid, err, result), "general")

		// 强制关闭
		cmd = fmt.Sprintf(`taskkill /F /PID %s`, pid)
		result, err = a.executeCommand("cmd", "/C", cmd)
		if err == nil {
			a.addLog("SUCCESS", fmt.Sprintf("强制关闭进程 %s (PID: %s) 成功", processName, pid), "general")
			successCount++
		} else {
			a.addLog("ERROR", fmt.Sprintf("强制关闭PID %s 失败: %v, 输出: %s", pid, err, result), "general")
			lastErr = err
		}
	}

	if successCount > 0 {
		a.addLog("SUCCESS", fmt.Sprintf("成功关闭 %d/%d 个 %s 进程", successCount, len(pids), processName), "general")
		return nil
	}

	return lastErr
}

// getProcessPIDs 获取指定进程名的所有PID
func (a *App) getProcessPIDs(processName string) ([]string, error) {
	// 使用tasklist获取PID
	cmd := fmt.Sprintf(`tasklist /FI "IMAGENAME eq %s" /FO CSV /NH`, processName)
	result, err := a.executeCommand("cmd", "/C", cmd)
	if err != nil {
		return nil, err
	}

	var pids []string
	lines := strings.Split(result, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.Contains(line, "INFO: No tasks") {
			continue
		}

		// CSV格式: "进程名","PID","会话名","会话#","内存使用"
		// 移除引号并分割
		line = strings.ReplaceAll(line, "\"", "")
		parts := strings.Split(line, ",")

		if len(parts) >= 2 {
			pid := strings.TrimSpace(parts[1])
			if pid != "" && pid != "PID" {
				pids = append(pids, pid)
			}
		}
	}

	return pids, nil
}

// closeGameByKnownProcesses 使用已知的游戏进程映射关闭游戏
func (a *App) closeGameByKnownProcesses(gameID string) error {
	// 已知游戏的进程映射
	gameProcessMap := map[string][]string{
		"578080": { // PUBG
			"TslGame.exe",
			"TslGame_BE.exe",
			"PUBG.exe",
			"ExecPubg.exe",
		},
		"730": { // CS:GO
			"csgo.exe",
			"cs2.exe",
		},
		"440": { // Team Fortress 2
			"hl2.exe",
		},
		"570": { // Dota 2
			"dota2.exe",
		},
		"252490": { // Rust
			"Rust.exe",
			"RustClient.exe",
		},
		"271590": { // Grand Theft Auto V
			"GTA5.exe",
			"GTAVLauncher.exe",
		},
		"431960": { // Wallpaper Engine
			"wallpaper32.exe",
			"wallpaper64.exe",
		},
	}

	processes, exists := gameProcessMap[gameID]
	if !exists {
		a.addLog("WARN", fmt.Sprintf("未知游戏ID: %s，尝试通用关闭方法", gameID), "general")
		return a.closeAllGameProcesses()
	}

	a.addLog("INFO", fmt.Sprintf("使用已知进程列表关闭游戏 %s: %v", gameID, processes), "general")

	var lastErr error
	var closedProcesses []string

	for _, processName := range processes {
		if a.isGameRunning(processName) {
			err := a.closeGameProcess(processName)
			if err != nil {
				lastErr = err
				a.addLog("ERROR", fmt.Sprintf("关闭 %s 失败: %v", processName, err), "general")
			} else {
				closedProcesses = append(closedProcesses, processName)
				a.addLog("SUCCESS", fmt.Sprintf("成功关闭 %s", processName), "general")
			}
		}
	}

	if len(closedProcesses) > 0 {
		a.addLog("SUCCESS", fmt.Sprintf("共关闭 %d 个游戏进程: %v", len(closedProcesses), closedProcesses), "general")
	} else {
		a.addLog("INFO", "未发现需要关闭的游戏进程", "general")
	}

	return lastErr
}

// closeAllGameProcesses 关闭所有可能的游戏进程（通用方法）
func (a *App) closeAllGameProcesses() error {
	a.addLog("INFO", "使用通用方法关闭所有可能的游戏进程", "general")

	// 获取所有进程
	processes, err := a.getAllRunningProcesses()
	if err != nil {
		return err
	}

	var closedProcesses []string
	var lastErr error

	// 查找可能的游戏进程（.exe文件且不是系统进程）
	for _, process := range processes {
		if a.isPossibleGameProcess(process) {
			err := a.closeGameProcess(process.Name)
			if err != nil {
				lastErr = err
				a.addLog("ERROR", fmt.Sprintf("关闭 %s 失败: %v", process.Name, err), "general")
			} else {
				closedProcesses = append(closedProcesses, process.Name)
				a.addLog("SUCCESS", fmt.Sprintf("成功关闭可能的游戏进程: %s", process.Name), "general")
			}
		}
	}

	if len(closedProcesses) > 0 {
		a.addLog("SUCCESS", fmt.Sprintf("共关闭 %d 个可能的游戏进程: %v", len(closedProcesses), closedProcesses), "general")
	}

	return lastErr
}

// isPossibleGameProcess 判断是否为可能的游戏进程
func (a *App) isPossibleGameProcess(process ProcessInfo) bool {
	// 排除系统进程和常见应用程序
	excludeProcesses := []string{
		"explorer.exe", "winlogon.exe", "csrss.exe", "smss.exe", "lsass.exe",
		"services.exe", "svchost.exe", "dwm.exe", "conhost.exe", "cmd.exe",
		"powershell.exe", "notepad.exe", "chrome.exe", "firefox.exe",
	}

	processName := strings.ToLower(process.Name)
	for _, exclude := range excludeProcesses {
		if processName == strings.ToLower(exclude) {
			return false
		}
	}

	// 只处理.exe文件且路径不在系统目录的进程
	if !strings.HasSuffix(processName, ".exe") {
		return false
	}

	// 排除系统目录的进程
	if process.Path != "" {
		lowerPath := strings.ToLower(process.Path)
		systemPaths := []string{
			"c:\\windows\\",
			"c:\\program files\\windows",
			"c:\\program files (x86)\\windows",
		}

		for _, sysPath := range systemPaths {
			if strings.HasPrefix(lowerPath, sysPath) {
				return false
			}
		}
	}

	return true
}
