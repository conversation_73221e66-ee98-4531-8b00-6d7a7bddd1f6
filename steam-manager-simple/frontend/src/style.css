* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  font-family: "Microsoft YaHei", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

body {
  margin: 0;
  padding: 0;
  color: #333;
  background: #f5f5f5;
  line-height: 1.6;
}

#app {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
  margin: 0;
  color: #2c3e50;
}

.stats {
  color: #666;
  font-size: 14px;
}

.toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background: #c0392b;
}

.btn-warning {
  background: #f39c12;
  color: white;
}

.btn-warning:hover {
  background: #e67e22;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-xs {
  padding: 2px 6px;
  font-size: 11px;
}

.btn-link {
  background: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.btn-link:hover {
  background: #3498db;
  color: white;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn:disabled:hover {
  background: inherit;
}

.account-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  min-height: 400px;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.account-item:last-child {
  border-bottom: none;
}

.account-info {
  flex: 1;
}

.username {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
}

.status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  margin-bottom: 5px;
  display: inline-block;
}

.status-正常 {
  background: #d4edda;
  color: #155724;
}

.status-封禁 {
  background: #f8d7da;
  color: #721c24;
}

.status-未知 {
  background: #e2e3e5;
  color: #383d41;
}

.status-vac封禁 {
  background: #f8d7da;
  color: #721c24;
}

.status-游戏封禁 {
  background: #fff3cd;
  color: #856404;
}

.steamid {
  color: #666;
  font-size: 11px;
  margin-bottom: 3px;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.pagination-controls {
  display: inline-flex;
  gap: 5px;
}

.notes {
  color: #666;
  font-size: 12px;
}

.account-actions {
  display: flex;
  gap: 5px;
}

.loading, .empty {
  text-align: center;
  padding: 50px;
  color: #666;
}

/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  position: relative;
}

.close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  position: absolute;
  right: 15px;
  top: 10px;
}

.close:hover {
  color: #000;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  text-align: right;
  margin-top: 20px;
}

.form-actions .btn {
  margin-left: 10px;
}

.help-text {
  margin-top: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.help-text p {
  margin: 5px 0;
}

.help-text a {
  color: #3498db;
  text-decoration: none;
}

.help-text a:hover {
  text-decoration: underline;
}

/* 配置模态框特定样式 */
.form-group label input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
  vertical-align: middle;
}

.form-group label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal;
}

.form-group label:hover {
  color: #3498db;
}

/* 安妮程序配置样式 */
#anniePasswordGroup {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  margin-top: 10px;
  transition: all 0.3s ease;
}

#anniePasswordGroup label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  display: block;
}

#anniePasswordGroup input[type="text"] {
  border: 2px solid #ced4da;
  transition: border-color 0.3s ease;
}

#anniePasswordGroup input[type="text"]:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  outline: none;
}

#anniePasswordGroup .help-text {
  background: #e7f3ff;
  border-left: 3px solid #007bff;
  margin-top: 8px;
  padding: 8px 12px;
  font-size: 11px;
}

/* 配置分组样式 */
.config-section {
  border-top: 1px solid #eee;
  padding-top: 15px;
  margin-top: 15px;
}

.config-section:first-child {
  border-top: none;
  padding-top: 0;
  margin-top: 0;
}

/* 日志区域样式 */
.logs-container {
  margin-top: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.logs-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.logs-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  max-height: 300px;
}

.log-empty {
  text-align: center;
  color: #666;
  padding: 20px;
  font-style: italic;
}

.log-entry {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  font-size: 13px;
  border-left: 3px solid transparent;
}

.log-entry:hover {
  background: #f8f9fa;
}

.log-time {
  color: #666;
  font-family: monospace;
  margin-right: 12px;
  min-width: 80px;
}

.log-level {
  font-weight: bold;
  margin-right: 12px;
  min-width: 80px;
}

.log-source {
  margin-right: 8px;
  font-size: 12px;
  opacity: 0.7;
}

.log-message {
  flex: 1;
  word-break: break-word;
}

/* 后端日志样式 */
.log-backend {
  border-left-width: 4px;
}

.log-backend .log-message {
  font-style: italic;
}

/* 前端日志样式 */
.log-frontend {
  border-left-width: 2px;
}

/* 登录账号高亮样式 - 简化版本 */
.logged-in-account {
  background: #e8f5e8 !important;
  border: 3px solid #4CAF50 !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.5) !important;
  position: relative;
}

.logged-in-account::before {
  content: "🟢 当前登录";
  position: absolute;
  top: -8px;
  right: 8px;
  background: #4CAF50;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  z-index: 10;
}

.logged-in-account .username {
  font-weight: bold !important;
  color: #1B5E20 !important;
  font-size: 1.1em !important;
}

.logged-in-account:hover {
  background: #c8e6c9 !important;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.6) !important;
}

/* 登录账号的按钮样式 */
.logged-in-account .btn-success {
  background: #4CAF50 !important;
  border-color: #4CAF50 !important;
  color: white !important;
  font-weight: bold;
}

.logged-in-account .btn-success:disabled {
  opacity: 0.8;
  cursor: not-allowed;
}

/* 等待状态按钮样式 */
.btn-secondary:disabled {
  background: #6c757d !important;
  border-color: #6c757d !important;
  color: white !important;
  opacity: 0.7;
  cursor: not-allowed;
}

/* 登录中状态按钮样式 */
.btn-warning:disabled {
  background: #ffc107 !important;
  border-color: #ffc107 !important;
  color: #212529 !important;
  opacity: 0.8;
  cursor: not-allowed;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 使用data属性选择器作为备用 */
[data-logged-in="true"] {
  background: #e8f5e8 !important;
  border: 3px solid #4CAF50 !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.5) !important;
}

/* 账号表格样式 */
.account-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 16px;
}

.account-table thead {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.account-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
  border-bottom: 2px solid #5a67d8;
}

.account-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: middle;
}

.account-table tbody tr:hover {
  background-color: #f7fafc;
}

/* 登录账号行高亮 */
.logged-in-row {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%) !important;
  border-left: 4px solid #4CAF50 !important;
}

.logged-in-row:hover {
  background: linear-gradient(135deg, #dcedc8 0%, #e8f5e8 100%) !important;
}

/* 序号单元格 */
.index-cell {
  text-align: center;
  font-weight: 600;
  color: #718096;
  background: #f7fafc;
  width: 50px;
}

/* 用户名单元格 */
.username-cell {
  font-weight: 600;
  color: #2d3748;
  position: relative;
}

.login-badge {
  background: #4CAF50;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  margin-left: 8px;
  font-weight: bold;
}

/* 状态徽章 */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  min-width: 60px;
  display: inline-block;
}

.status-正常 {
  background: #c6f6d5;
  color: #22543d;
}

.status-vac封禁 {
  background: #fed7d7;
  color: #742a2a;
}

.status-有游戏封禁 {
  background: #feebc8;
  color: #7b341e;
}

.status-未知 {
  background: #e2e8f0;
  color: #4a5568;
}

/* 时间单元格 */
.time-cell {
  color: #718096;
  font-size: 13px;
}

/* 备注单元格 */
.notes-cell {
  color: #4a5568;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* SteamID单元格 */
.steamid-cell {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.steamid-text {
  color: #4a5568;
  background: #f7fafc;
  padding: 2px 6px;
  border-radius: 4px;
}

.steamid-status {
  color: #ed8936;
  font-style: italic;
  font-size: 12px;
}

.steamid-failed {
  color: #e53e3e;
  font-style: italic;
  font-size: 12px;
  font-weight: bold;
}

/* 操作单元格 */
.actions-cell {
  white-space: nowrap;
}

.actions-cell .btn {
  margin-right: 4px;
  margin-bottom: 2px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .account-table {
    font-size: 13px;
  }

  .account-table th,
  .account-table td {
    padding: 8px 12px;
  }

  .notes-cell {
    max-width: 100px;
  }
}

@media (max-width: 768px) {
  .account-table {
    font-size: 12px;
  }

  .account-table th,
  .account-table td {
    padding: 6px 8px;
  }

  .actions-cell .btn {
    padding: 2px 6px;
    font-size: 11px;
  }

  .notes-cell {
    display: none;
  }

  .steamid-cell {
    display: none;
  }

  .index-cell {
    width: 40px;
    padding: 6px 4px;
  }
}

/* 分页控件样式 */
.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.pagination-controls .btn {
  min-width: 40px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-ellipsis {
  color: #6c757d;
  font-weight: bold;
  padding: 0 8px;
  display: flex;
  align-items: center;
}

.pagination-info {
  color: #6c757d;
  font-size: 14px;
  margin-left: 16px;
  white-space: nowrap;
}

/* 响应式分页 */
@media (max-width: 768px) {
  .pagination-controls {
    gap: 4px;
    margin: 16px 0;
  }

  .pagination-controls .btn {
    min-width: 36px;
    height: 28px;
    font-size: 12px;
    padding: 2px 6px;
  }

  .pagination-info {
    font-size: 12px;
    margin-left: 8px;
  }

  .pagination-ellipsis {
    padding: 0 4px;
  }
}

/* 右上角斜角水印 */
.watermark-banner {
  position: fixed;
  top: 0;
  right: 0;
  width: 120px;
  height: 120px;
  overflow: hidden;
  z-index: 1000;
  pointer-events: none;
}

.watermark-banner::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 120px 120px 0;
  border-color: transparent #667eea transparent transparent;
  opacity: 0.8;
}

.watermark-banner span {
  position: absolute;
  top: 25px;
  right: 15px;
  color: white;
  font-size: 14px;
  font-weight: bold;
  transform: rotate(45deg);
  transform-origin: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

/* 不同级别的日志样式 */
.log-success {
  background: #d4edda;
  border-left-color: #28a745;
}

.log-success .log-level {
  color: #155724;
}

.log-error {
  background: #f8d7da;
  border-left-color: #dc3545;
}

.log-error .log-level {
  color: #721c24;
}

.log-warn {
  background: #fff3cd;
  border-left-color: #ffc107;
}

.log-warn .log-level {
  color: #856404;
}

.log-info {
  background: #d1ecf1;
  border-left-color: #17a2b8;
}

.log-info .log-level {
  color: #0c5460;
}

/* 消息提示样式 */
.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-weight: bold;
  z-index: 1001;
  animation: slideIn 0.3s ease-out;
}

.message-success {
  background: #27ae60;
}

.message-error {
  background: #e74c3c;
}

.message-info {
  background: #3498db;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 自定义提示框样式 */
.custom-modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  animation: fadeIn 0.3s ease-out;
}

.custom-modal-content {
  background-color: white;
  margin: 10% auto;
  padding: 0;
  border-radius: 12px;
  width: 90%;
  max-width: 450px;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-icon {
  font-size: 24px;
  margin-right: 12px;
  min-width: 24px;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.modal-message {
  padding: 24px;
  font-size: 16px;
  line-height: 1.5;
  color: #2c3e50;
  text-align: center;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 24px;
  background: #f8f9fa;
}

.modal-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 80px;
}

.modal-btn-primary {
  background: #3498db;
  color: white;
}

.modal-btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.modal-btn-secondary {
  background: #95a5a6;
  color: white;
}

.modal-btn-secondary:hover {
  background: #7f8c8d;
  transform: translateY(-1px);
}

.modal-btn-danger {
  background: #e74c3c;
  color: white;
}

.modal-btn-danger:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

/* 不同类型的提示框图标和颜色 */
.custom-modal.modal-info .modal-header {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.custom-modal.modal-success .modal-header {
  background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.custom-modal.modal-warning .modal-header {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.custom-modal.modal-error .modal-header {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.custom-modal.modal-confirm .modal-header {
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .header {
    flex-direction: column;
    gap: 10px;
  }

  .toolbar {
    flex-wrap: wrap;
  }

  .account-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .account-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .modal-content {
    margin: 10% auto;
    width: 95%;
  }

  .custom-modal-content {
    margin: 15% auto;
    width: 95%;
    max-width: none;
  }

  .modal-header {
    padding: 16px 20px 12px;
  }

  .modal-icon {
    font-size: 20px;
    margin-right: 10px;
  }

  .modal-title {
    font-size: 16px;
  }

  .modal-message {
    padding: 20px;
    font-size: 15px;
  }

  .modal-buttons {
    padding: 12px 20px 20px;
    gap: 8px;
  }

  .modal-btn {
    padding: 8px 16px;
    font-size: 13px;
    min-width: 70px;
  }
}
