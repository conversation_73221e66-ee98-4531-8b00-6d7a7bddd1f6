@echo off
echo Building Steam Manager Web v2...
echo.

REM 检查Wails是否安装
wails version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Wails is not installed
    echo Please install Wails: go install github.com/wailsapp/wails/v2/cmd/wails@latest
    pause
    exit /b 1
)

REM 安装依赖
echo Installing dependencies...
go mod tidy

REM 构建应用
echo Building application...
wails build

echo.
echo Build completed! Executable is in build/bin/
echo.
pause
