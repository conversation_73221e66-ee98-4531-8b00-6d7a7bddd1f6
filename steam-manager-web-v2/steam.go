package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"syscall"
	"time"
)

// LoginSteam Steam登录
func (a *App) LoginSteam(username, password string) error {
	// 简单验证
	if username == "" || password == "" {
		return fmt.Errorf("用户名和密码不能为空")
	}

	// 启动Steam登录
	err := a.launchSteamWithAccount(username, password)
	if err != nil {
		return err
	}

	// 更新账号登录状态
	go func() {
		// 等待Steam启动完成
		time.Sleep(3 * time.Second)
		a.updateAccountLastLogin(username)
	}()

	return nil
}

// launchSteamWithAccount 启动Steam并登录指定账号
func (a *App) launchSteamWithAccount(username, password string) error {
	// 如果配置了自动关闭游戏，先关闭游戏
	if a.config.Steam.GameConfig.AutoCloseGame {
		a.addLog("INFO", "正在关闭现有游戏进程...", "general")
		if err := a.closePUBG(); err != nil {
			a.addLog("WARN", fmt.Sprintf("关闭游戏失败: %v", err), "general")
		}
	}

	// 检查Steam是否正在运行
	if a.isSteamRunning() {
		// 如果Steam正在运行，先关闭它
		a.addLog("INFO", "正在关闭现有Steam进程...", "general")
		if err := a.closeSteam(); err != nil {
			a.addLog("WARN", fmt.Sprintf("关闭Steam失败: %v", err), "general")
		}
		// 等待Steam完全关闭
		time.Sleep(3 * time.Second)
	}

	// 启动Steam并登录
	return a.startSteamWithLogin(username, password)
}

// isSteamRunning 检查Steam是否正在运行
func (a *App) isSteamRunning() bool {
	// 使用tasklist命令检查Steam进程
	result, err := a.executeCommand("tasklist", "/FI", "IMAGENAME eq Steam.exe")
	if err != nil {
		return false
	}

	// 检查输出中是否包含Steam.exe
	return strings.Contains(strings.ToLower(result), "steam.exe")
}

// closeSteam 关闭Steam
func (a *App) closeSteam() error {
	// 使用taskkill命令关闭Steam
	cmd := `taskkill /F /IM Steam.exe`
	_, err := a.executeCommand("cmd", "/C", cmd)
	return err
}

// isGameRunning 检查指定游戏进程是否正在运行
func (a *App) isGameRunning(processName string) bool {
	result, err := a.executeCommand("tasklist", "/FI", fmt.Sprintf("IMAGENAME eq %s", processName))
	if err != nil {
		return false
	}
	// 检查输出中是否包含进程名
	return strings.Contains(strings.ToLower(result), strings.ToLower(processName))
}

// isPUBGRunning 检查PUBG是否正在运行
func (a *App) isPUBGRunning() bool {
	// PUBG的可能进程名
	pubgProcesses := []string{
		"TslGame.exe",
		"TslGame_BE.exe",
		"PUBG.exe",
		"ExecPubg.exe",
	}

	for _, processName := range pubgProcesses {
		if a.isGameRunning(processName) {
			return true
		}
	}
	return false
}

// closeGame 关闭指定的游戏进程
func (a *App) closeGame(processName string) error {
	if !a.isGameRunning(processName) {
		return nil // 游戏没有运行，无需关闭
	}

	// 尝试优雅关闭
	cmd := fmt.Sprintf(`taskkill /IM %s`, processName)
	_, err := a.executeCommand("cmd", "/C", cmd)
	if err == nil {
		// 等待进程关闭
		time.Sleep(3 * time.Second)
		if !a.isGameRunning(processName) {
			return nil
		}
	}

	// 强制关闭
	cmd = fmt.Sprintf(`taskkill /F /IM %s`, processName)
	_, err = a.executeCommand("cmd", "/C", cmd)
	return err
}

// closePUBG 关闭PUBG游戏
func (a *App) closePUBG() error {
	// PUBG的可能进程名
	pubgProcesses := []string{
		"TslGame.exe",
		"TslGame_BE.exe",
		"PUBG.exe",
		"ExecPubg.exe",
	}

	var lastErr error
	for _, processName := range pubgProcesses {
		err := a.closeGame(processName)
		if err != nil {
			lastErr = err
			a.addLog("ERROR", fmt.Sprintf("关闭 %s 失败: %v", processName, err), "general")
		} else if a.isGameRunning(processName) {
			a.addLog("SUCCESS", fmt.Sprintf("成功关闭 %s", processName), "general")
		}
	}

	return lastErr
}

// startSteamWithLogin 启动Steam并登录
func (a *App) startSteamWithLogin(username, password string) error {
	// 查找Steam安装路径
	steamPath := a.findSteamPath()
	if steamPath == "" {
		return fmt.Errorf("未找到Steam安装路径")
	}

	// 构建Steam启动命令
	steamExe := filepath.Join(steamPath, "Steam.exe")

	// 启动Steam（不等待完成，隐藏窗口）
	cmd := exec.Command(steamExe, "-login", username, password)

	// 在Windows上隐藏命令行窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	err := cmd.Start()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("启动Steam失败: %v", err), "general")
		return fmt.Errorf("启动Steam失败: %v", err)
	}

	a.addLog("SUCCESS", fmt.Sprintf("Steam启动命令已执行: %s -login %s ***", steamExe, username), "general")

	// 等待Steam启动
	time.Sleep(5 * time.Second)

	// 如果配置了自动启动游戏，则启动游戏
	if a.config.Steam.AutoLaunchGame {
		go func() {
			// 等待Steam完全启动
			time.Sleep(10 * time.Second)

			// 如果启用了安妮程序，先启动安妮程序
			annieSuccess := false
			if a.config.Annie.Enabled {
				a.addLog("INFO", "Steam登录成功，开始启动安妮程序", "general")
				if err := a.launchAnnie(); err != nil {
					a.addLog("ERROR", fmt.Sprintf("启动安妮程序失败: %v", err), "general")
					a.addLog("INFO", "安妮程序启动失败，跳过游戏启动", "general")
					return // 安妮程序启动失败，不启动游戏
				} else {
					// 安妮程序启动成功，等待其处理完成后再启动游戏
					a.addLog("INFO", "安妮程序启动成功，等待处理完成后启动游戏", "general")
					// 等待并处理弹窗、进程退出和文件消失
					if err := a.handleAnnieDialogs(); err != nil {
						a.addLog("ERROR", fmt.Sprintf("安妮程序处理失败: %v", err), "general")
						a.addLog("INFO", "安妮程序处理失败，跳过游戏启动", "general")
						return // 安妮程序处理失败，不启动游戏
					}
					annieSuccess = true
				}
			}

			// 只有在安妮程序未启用或处理完成后才启动游戏
			if !a.config.Annie.Enabled || annieSuccess {
				if err := a.launchGame(); err != nil {
					a.addLog("ERROR", fmt.Sprintf("启动游戏失败: %v", err), "general")
				}
			}
		}()
	}

	return nil
}

// findSteamPath 查找Steam安装路径
func (a *App) findSteamPath() string {
	// 常见的Steam安装路径
	commonPaths := []string{
		`C:\Program Files (x86)\Steam`,
		`C:\Program Files\Steam`,
		`D:\Steam`,
		`E:\Steam`,
		`F:\Steam`,
	}

	// 检查常见路径
	for _, path := range commonPaths {
		steamExe := filepath.Join(path, "Steam.exe")
		if _, err := os.Stat(steamExe); err == nil {
			return path
		}
	}

	// 尝试从注册表读取
	steamPath := a.getSteamPathFromRegistry()
	if steamPath != "" {
		return steamPath
	}

	return ""
}

// getSteamPathFromRegistry 从注册表获取Steam路径
func (a *App) getSteamPathFromRegistry() string {
	// 使用reg命令查询注册表
	cmd := `reg query "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Valve\Steam" /v InstallPath 2>NUL`
	result, err := a.executeCommand("cmd", "/C", cmd)
	if err != nil {
		return ""
	}

	// 解析注册表输出
	lines := strings.Split(result, "\n")
	for _, line := range lines {
		if strings.Contains(line, "InstallPath") && strings.Contains(line, "REG_SZ") {
			parts := strings.Fields(line)
			if len(parts) >= 3 {
				return strings.Join(parts[2:], " ")
			}
		}
	}

	return ""
}

// launchGame 启动游戏
func (a *App) launchGame() error {
	gameID := a.config.Steam.GameConfig.GameID
	if gameID == "" {
		gameID = "578080" // PUBG的Steam ID
	}

	// 使用Steam URL协议启动游戏
	steamURL := fmt.Sprintf("steam://rungameid/%s", gameID)

	// 使用start命令启动URL（隐藏窗口）
	cmd := exec.Command("cmd", "/C", "start", steamURL)

	// 在Windows上隐藏命令行窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	err := cmd.Start()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("启动游戏失败: %v", err), "general")
		return err
	}

	a.addLog("SUCCESS", fmt.Sprintf("游戏启动命令已执行: %s", steamURL), "general")
	return nil
}

// LaunchGame 公开的启动游戏方法
func (a *App) LaunchGame() error {
	return a.launchGame()
}

// updateAccountLastLogin 更新账号最后登录时间
func (a *App) updateAccountLastLogin(username string) {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	now := time.Now()
	for i, account := range a.accounts {
		// 重置所有账号的登录状态
		a.accounts[i].IsLoggedIn = false

		// 设置当前登录账号
		if account.Username == username {
			a.accounts[i].LastLogin = now
			a.accounts[i].IsLoggedIn = true
			a.accounts[i].UpdatedAt = now

			// 更新数据库
			if a.db != nil {
				_, err := a.db.Exec(`
					UPDATE accounts SET last_login = ?, updated_at = ? WHERE id = ?
				`, now, now, account.ID)
				if err != nil {
					a.addLog("ERROR", fmt.Sprintf("更新账号登录时间失败: %v", err), "database")
				}
			}

			a.addLog("INFO", fmt.Sprintf("账号 %s 登录状态已更新", username), "general")
			break
		}
	}
}

// ============ 缺失的方法实现 ============

// launchAnnie 启动安妮程序
func (a *App) launchAnnie() error {
	a.addLog("INFO", "安妮程序功能开发中...", "general")
	return nil
}

// handleAnnieDialogs 处理安妮程序弹窗
func (a *App) handleAnnieDialogs() error {
	a.addLog("INFO", "安妮程序弹窗处理功能开发中...", "general")
	return nil
}
