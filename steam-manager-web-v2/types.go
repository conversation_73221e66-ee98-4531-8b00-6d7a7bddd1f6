package main

import "time"

// Account 账号结构 - 扩展原有结构，增加用户ID
type Account struct {
	ID            int       `json:"id"`
	UserID        int       `json:"userId"`        // 网络版新增：所属用户ID
	Username      string    `json:"username"`
	Password      string    `json:"password"`
	Notes         string    `json:"notes"`
	Status        string    `json:"status"`
	SteamID       string    `json:"steamId"`
	PUBGBanStatus int       `json:"pubgBanStatus"`
	LastLogin     time.Time `json:"lastLogin"`
	LastChecked   time.Time `json:"lastChecked"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	IsLoggedIn    bool      `json:"isLoggedIn"` // 标记是否为当前登录账号
}

// Config 配置结构 - 扩展原有结构，增加网络配置
type Config struct {
	Database struct {
		Path string `json:"path"`
	} `json:"database"`
	
	// 网络版新增配置
	Network struct {
		Enabled  bool   `json:"enabled"`   // 是否启用网络模式
		Database struct {
			Type     string `json:"type"`     // mysql 或 sqlite
			Host     string `json:"host"`
			Port     int    `json:"port"`
			Username string `json:"username"`
			Password string `json:"password"`
			Database string `json:"database"`
		} `json:"database"`
	} `json:"network"`
	
	Steam struct {
		AutoLaunchGame bool `json:"auto_launch_game"`
		GameConfig     struct {
			GameID        string `json:"game_id"`
			GameName      string `json:"game_name"`
			LaunchArgs    string `json:"launch_args"`
			AutoCloseGame bool   `json:"auto_close_game"`
		} `json:"game_config"`
	} `json:"steam"`
	Annie struct {
		Enabled     bool   `json:"enabled"`
		RarPassword string `json:"rar_password"`
	} `json:"annie"`
	API struct {
		SteamAPIKeys []string `json:"steam_api_keys"`
	} `json:"api"`
}

// User 用户结构 - 网络版新增
type User struct {
	ID         int        `json:"id"`
	Username   string     `json:"username"`
	Email      string     `json:"email"`
	Password   string     `json:"-"`              // 不返回密码
	IsPremium  bool       `json:"isPremium"`
	ExpireTime *time.Time `json:"expireTime"`
	CreatedAt  time.Time  `json:"createdAt"`
	UpdatedAt  time.Time  `json:"updatedAt"`
}

// Subscription 订阅信息 - 网络版新增
type Subscription struct {
	ID        int       `json:"id"`
	UserID    int       `json:"userId"`
	Type      string    `json:"type"`      // free, premium
	StartTime time.Time `json:"startTime"`
	EndTime   time.Time `json:"endTime"`
	IsActive  bool      `json:"isActive"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// Order 订单信息 - 网络版新增
type Order struct {
	ID          int       `json:"id"`
	UserID      int       `json:"userId"`
	OrderNo     string    `json:"orderNo"`
	Amount      float64   `json:"amount"`
	Status      string    `json:"status"`      // pending, paid, cancelled
	PayMethod   string    `json:"payMethod"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// SteamGame Steam游戏信息
type SteamGame struct {
	AppID string `json:"app_id"`
	Name  string `json:"name"`
}

// SteamBanInfo Steam封禁信息
type SteamBanInfo struct {
	SteamID          string `json:"SteamId"`
	CommunityBanned  bool   `json:"CommunityBanned"`
	VACBanned        bool   `json:"VACBanned"`
	NumberOfVACBans  int    `json:"NumberOfVACBans"`
	DaysSinceLastBan int    `json:"DaysSinceLastBan"`
	NumberOfGameBans int    `json:"NumberOfGameBans"`
	EconomyBan       string `json:"EconomyBan"`
}

// SteamBanResponse Steam API响应
type SteamBanResponse struct {
	Players []SteamBanInfo `json:"players"`
}

// SteamUserInfo Steam用户信息
type SteamUserInfo struct {
	SteamID     string `json:"steamid"`
	PersonaName string `json:"personaname"`
	ProfileURL  string `json:"profileurl"`
}

// SteamUserResponse Steam用户API响应
type SteamUserResponse struct {
	Response struct {
		Players []SteamUserInfo `json:"players"`
	} `json:"response"`
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp string `json:"timestamp"`
	Level     string `json:"level"`
	Message   string `json:"message"`
	Category  string `json:"category"` // steamid, bancheck, general, auth, network
}

// AccountListResponse 账号列表响应
type AccountListResponse struct {
	Accounts    []Account `json:"accounts"`
	Total       int       `json:"total"`
	CurrentPage int       `json:"currentPage"`
	TotalPages  int       `json:"totalPages"`
	PageSize    int       `json:"pageSize"`
}

// ImportAccount 导入账号结构
type ImportAccount struct {
	Username string
	Password string
}

// LoginRequest 登录请求 - 网络版新增
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// RegisterRequest 注册请求 - 网络版新增
type RegisterRequest struct {
	Username string `json:"username"`
	Email    string `json:"email"`
	Password string `json:"password"`
}

// UserStats 用户统计 - 网络版新增
type UserStats struct {
	TotalAccounts int `json:"totalAccounts"`
	NormalAccounts int `json:"normalAccounts"`
	BannedAccounts int `json:"bannedAccounts"`
	UnknownAccounts int `json:"unknownAccounts"`
}

// NetworkStatus 网络状态 - 网络版新增
type NetworkStatus struct {
	IsNetworkMode bool  `json:"isNetworkMode"`
	IsLoggedIn    bool  `json:"isLoggedIn"`
	User          *User `json:"user"`
}
