# Steam账号管理工具 - 网络版

基于 `steam-manager-simple` 项目开发的网络版本，在保持原有所有功能的基础上，增加了用户管理、云端存储、订阅服务等网络功能。

## 🚀 新增功能

### 🌐 网络版特性
- **用户系统** - 注册、登录、多用户支持
- **云端存储** - MySQL数据库，支持多设备同步
- **订阅服务** - 免费版/高级版，20元/年
- **数据隔离** - 每个用户只能看到自己的账号
- **本地兼容** - 无网络时自动降级到本地模式

### 📊 版本对比

| 功能 | 普通版 | 网络版 |
|------|--------|--------|
| Steam账号管理 | ✅ | ✅ |
| 批量导入 | ✅ | ✅ |
| SteamID获取 | ✅ | ✅ |
| 封禁检测 | ✅ | ✅ |
| Steam登录 | ✅ | ✅ |
| 游戏启动 | ✅ | ✅ |
| 安妮程序集成 | ✅ | ✅ |
| 用户系统 | ❌ | ✅ |
| 云端存储 | ❌ | ✅ |
| 多设备同步 | ❌ | ✅ |
| 订阅服务 | ❌ | ✅ |

## 🛠️ 技术架构

- **前端**: Wails v2 + HTML/CSS/JavaScript
- **后端**: Go 1.21+
- **数据库**: MySQL (网络模式) / SQLite (本地模式)
- **认证**: bcrypt密码加密
- **部署**: 单文件可执行程序

## 📦 安装使用

### 环境要求
- Windows 10/11
- Go 1.21+ (开发环境)
- MySQL 5.7+ (网络模式，可选)

### 快速开始

1. **下载程序**
   ```bash
   # 下载预编译版本或从源码构建
   git clone <repository-url>
   cd steam-manager-web-v2
   ```

2. **配置文件**
   ```bash
   # 复制配置示例
   cp config.example.json config.json
   # 根据需要修改配置
   ```

3. **运行程序**
   ```bash
   # 开发模式
   wails dev
   
   # 或构建后运行
   wails build
   ./build/bin/steam-manager-web-v2.exe
   ```

### 配置说明

#### 本地模式配置
```json
{
  "network": {
    "enabled": false
  }
}
```

#### 网络模式配置
```json
{
  "network": {
    "enabled": true,
    "database": {
      "type": "mysql",
      "host": "localhost",
      "port": 3306,
      "username": "root",
      "password": "your_password",
      "database": "steam_manager"
    }
  }
}
```

## 🗄️ 数据库设置

### MySQL数据库创建
```sql
-- 创建数据库
CREATE DATABASE steam_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'steam_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON steam_manager.* TO 'steam_user'@'localhost';
FLUSH PRIVILEGES;
```

### 表结构
程序会自动创建以下表：
- `users` - 用户表
- `accounts` - Steam账号表
- `subscriptions` - 订阅表
- `orders` - 订单表

## 🔧 功能说明

### 用户系统
- **注册**: 用户名、邮箱、密码
- **登录**: 自动保存登录状态
- **权限**: 免费版/高级版功能区分

### 账号管理
- **添加账号**: 支持单个和批量添加
- **编辑账号**: 修改用户名、密码、备注
- **删除账号**: 支持单个删除
- **状态检测**: 自动获取SteamID和检测封禁状态

### Steam功能
- **登录**: 自动切换Steam账号
- **游戏启动**: 支持自动启动PUBG等游戏
- **进程管理**: 自动关闭现有Steam和游戏进程

### 订阅服务
- **免费版**: 本地数据存储，基础功能
- **高级版**: 云端存储，多设备同步，20元/年

## 🔄 数据迁移

### 从普通版迁移
1. 导出普通版账号数据
2. 在网络版中使用批量导入功能
3. 数据会自动关联到当前登录用户

### 本地模式与网络模式切换
- 本地模式数据存储在SQLite文件中
- 网络模式数据存储在MySQL数据库中
- 可以通过配置文件随时切换模式

## 🚀 部署指南

### 开发环境
```bash
# 安装Wails
go install github.com/wailsapp/wails/v2/cmd/wails@latest

# 运行开发服务器
wails dev
```

### 生产构建
```bash
# 构建Windows版本
wails build

# 构建其他平台
wails build -platform windows/amd64
wails build -platform linux/amd64
wails build -platform darwin/amd64
```

### 服务器部署
1. 部署MySQL数据库
2. 配置网络连接
3. 分发客户端程序
4. 用户下载安装即可使用

## 📝 更新日志

### v2.0.0 (网络版)
- ✅ 新增用户注册/登录系统
- ✅ 新增MySQL云端存储支持
- ✅ 新增订阅服务系统
- ✅ 新增多用户数据隔离
- ✅ 保持所有原有功能
- ✅ 支持本地模式兼容

### v1.0.0 (普通版)
- ✅ Steam账号管理
- ✅ 批量导入功能
- ✅ SteamID自动获取
- ✅ 封禁状态检测
- ✅ Steam自动登录
- ✅ 游戏自动启动

## 🤝 贡献

欢迎提交Issue和Pull Request来帮助改进项目！

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 支持

如果您在使用过程中遇到问题：
1. 查看本README的相关说明
2. 检查配置文件是否正确
3. 查看程序日志获取错误信息
4. 提交Issue描述问题

---

**注意**: 本工具仅供学习和研究使用，请遵守Steam服务条款和相关法律法规。
