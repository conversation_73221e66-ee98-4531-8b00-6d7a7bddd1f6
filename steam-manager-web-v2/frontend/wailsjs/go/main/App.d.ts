// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT
import {main} from '../models';

export function AddAccount(arg1:string,arg2:string,arg3:string):Promise<void>;

export function AddAccountWithoutSteamID(arg1:string,arg2:string,arg3:string):Promise<void>;

export function ClearLogs():Promise<void>;

export function DeleteAccount(arg1:number):Promise<void>;

export function GetAccountStats():Promise<Record<string, number>>;

export function GetAccounts():Promise<Array<main.Account>>;

export function GetAccountsPaginated(arg1:number,arg2:number):Promise<main.AccountListResponse>;

export function GetConfig():Promise<main.Config>;

export function GetCurrentUser():Promise<main.User>;

export function GetLogs():Promise<Array<main.LogEntry>>;

export function GetNetworkStatus():Promise<Record<string, any>>;

export function GetUserSubscription():Promise<main.Subscription>;

export function Greet(arg1:string):Promise<string>;

export function IsLoggedIn():Promise<boolean>;

export function IsNetworkMode():Promise<boolean>;

export function LaunchGame():Promise<void>;

export function Login(arg1:string,arg2:string):Promise<main.User>;

export function LoginSteam(arg1:string,arg2:string):Promise<void>;

export function Logout():Promise<void>;

export function Register(arg1:string,arg2:string,arg3:string):Promise<main.User>;

export function SetAccountSteamID(arg1:number,arg2:string):Promise<void>;

export function UpdateAccount(arg1:number,arg2:string,arg3:string,arg4:string):Promise<void>;

export function UpdateConfig(arg1:boolean,arg2:boolean,arg3:boolean,arg4:string,arg5:string,arg6:string):Promise<void>;

export function UpdateNetworkConfig(arg1:boolean,arg2:string,arg3:string,arg4:string,arg5:string,arg6:string,arg7:number):Promise<void>;

export function UpgradeToPremium():Promise<void>;
