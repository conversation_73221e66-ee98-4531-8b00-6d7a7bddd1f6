// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function AddAccount(arg1, arg2, arg3) {
  return window['go']['main']['App']['AddAccount'](arg1, arg2, arg3);
}

export function AddAccountWithoutSteamID(arg1, arg2, arg3) {
  return window['go']['main']['App']['AddAccountWithoutSteamID'](arg1, arg2, arg3);
}

export function ClearLogs() {
  return window['go']['main']['App']['ClearLogs']();
}

export function DeleteAccount(arg1) {
  return window['go']['main']['App']['DeleteAccount'](arg1);
}

export function GetAccountStats() {
  return window['go']['main']['App']['GetAccountStats']();
}

export function GetAccounts() {
  return window['go']['main']['App']['GetAccounts']();
}

export function GetAccountsPaginated(arg1, arg2) {
  return window['go']['main']['App']['GetAccountsPaginated'](arg1, arg2);
}

export function GetConfig() {
  return window['go']['main']['App']['GetConfig']();
}

export function GetCurrentUser() {
  return window['go']['main']['App']['GetCurrentUser']();
}

export function GetLogs() {
  return window['go']['main']['App']['GetLogs']();
}

export function GetNetworkStatus() {
  return window['go']['main']['App']['GetNetworkStatus']();
}

export function GetUserSubscription() {
  return window['go']['main']['App']['GetUserSubscription']();
}

export function Greet(arg1) {
  return window['go']['main']['App']['Greet'](arg1);
}

export function IsLoggedIn() {
  return window['go']['main']['App']['IsLoggedIn']();
}

export function IsNetworkMode() {
  return window['go']['main']['App']['IsNetworkMode']();
}

export function LaunchGame() {
  return window['go']['main']['App']['LaunchGame']();
}

export function Login(arg1, arg2) {
  return window['go']['main']['App']['Login'](arg1, arg2);
}

export function LoginSteam(arg1, arg2) {
  return window['go']['main']['App']['LoginSteam'](arg1, arg2);
}

export function Logout() {
  return window['go']['main']['App']['Logout']();
}

export function Register(arg1, arg2, arg3) {
  return window['go']['main']['App']['Register'](arg1, arg2, arg3);
}

export function SetAccountSteamID(arg1, arg2) {
  return window['go']['main']['App']['SetAccountSteamID'](arg1, arg2);
}

export function UpdateAccount(arg1, arg2, arg3, arg4) {
  return window['go']['main']['App']['UpdateAccount'](arg1, arg2, arg3, arg4);
}

export function UpdateConfig(arg1, arg2, arg3, arg4, arg5, arg6) {
  return window['go']['main']['App']['UpdateConfig'](arg1, arg2, arg3, arg4, arg5, arg6);
}

export function UpdateNetworkConfig(arg1, arg2, arg3, arg4, arg5, arg6, arg7) {
  return window['go']['main']['App']['UpdateNetworkConfig'](arg1, arg2, arg3, arg4, arg5, arg6, arg7);
}

export function UpgradeToPremium() {
  return window['go']['main']['App']['UpgradeToPremium']();
}
