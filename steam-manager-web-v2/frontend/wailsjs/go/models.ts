export namespace main {
	
	export class Account {
	    id: number;
	    userId: number;
	    username: string;
	    password: string;
	    notes: string;
	    status: string;
	    steamId: string;
	    pubgBanStatus: number;
	    // Go type: time
	    lastLogin: any;
	    // Go type: time
	    lastChecked: any;
	    // Go type: time
	    createdAt: any;
	    // Go type: time
	    updatedAt: any;
	    isLoggedIn: boolean;
	
	    static createFrom(source: any = {}) {
	        return new Account(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.userId = source["userId"];
	        this.username = source["username"];
	        this.password = source["password"];
	        this.notes = source["notes"];
	        this.status = source["status"];
	        this.steamId = source["steamId"];
	        this.pubgBanStatus = source["pubgBanStatus"];
	        this.lastLogin = this.convertValues(source["lastLogin"], null);
	        this.lastChecked = this.convertValues(source["lastChecked"], null);
	        this.createdAt = this.convertValues(source["createdAt"], null);
	        this.updatedAt = this.convertValues(source["updatedAt"], null);
	        this.isLoggedIn = source["isLoggedIn"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class AccountListResponse {
	    accounts: Account[];
	    total: number;
	    currentPage: number;
	    totalPages: number;
	    pageSize: number;
	
	    static createFrom(source: any = {}) {
	        return new AccountListResponse(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.accounts = this.convertValues(source["accounts"], Account);
	        this.total = source["total"];
	        this.currentPage = source["currentPage"];
	        this.totalPages = source["totalPages"];
	        this.pageSize = source["pageSize"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Config {
	    // Go type: struct { Path string "json:\"path\"" }
	    database: any;
	    // Go type: struct { Enabled bool "json:\"enabled\""; Database struct { Type string "json:\"type\""; Host string "json:\"host\""; Port int "json:\"port\""; Username string "json:\"username\""; Password string "json:\"password\""; Database string "json:\"database\"" } "json:\"database\"" }
	    network: any;
	    // Go type: struct { AutoLaunchGame bool "json:\"auto_launch_game\""; GameConfig struct { GameID string "json:\"game_id\""; GameName string "json:\"game_name\""; LaunchArgs string "json:\"launch_args\""; AutoCloseGame bool "json:\"auto_close_game\"" } "json:\"game_config\"" }
	    steam: any;
	    // Go type: struct { Enabled bool "json:\"enabled\""; RarPassword string "json:\"rar_password\"" }
	    annie: any;
	    // Go type: struct { SteamAPIKeys []string "json:\"steam_api_keys\"" }
	    api: any;
	
	    static createFrom(source: any = {}) {
	        return new Config(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.database = this.convertValues(source["database"], Object);
	        this.network = this.convertValues(source["network"], Object);
	        this.steam = this.convertValues(source["steam"], Object);
	        this.annie = this.convertValues(source["annie"], Object);
	        this.api = this.convertValues(source["api"], Object);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class LogEntry {
	    timestamp: string;
	    level: string;
	    message: string;
	    category: string;
	
	    static createFrom(source: any = {}) {
	        return new LogEntry(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.timestamp = source["timestamp"];
	        this.level = source["level"];
	        this.message = source["message"];
	        this.category = source["category"];
	    }
	}
	export class Subscription {
	    id: number;
	    userId: number;
	    type: string;
	    // Go type: time
	    startTime: any;
	    // Go type: time
	    endTime: any;
	    isActive: boolean;
	    // Go type: time
	    createdAt: any;
	    // Go type: time
	    updatedAt: any;
	
	    static createFrom(source: any = {}) {
	        return new Subscription(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.userId = source["userId"];
	        this.type = source["type"];
	        this.startTime = this.convertValues(source["startTime"], null);
	        this.endTime = this.convertValues(source["endTime"], null);
	        this.isActive = source["isActive"];
	        this.createdAt = this.convertValues(source["createdAt"], null);
	        this.updatedAt = this.convertValues(source["updatedAt"], null);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class User {
	    id: number;
	    username: string;
	    email: string;
	    isPremium: boolean;
	    // Go type: time
	    expireTime?: any;
	    // Go type: time
	    createdAt: any;
	    // Go type: time
	    updatedAt: any;
	
	    static createFrom(source: any = {}) {
	        return new User(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.username = source["username"];
	        this.email = source["email"];
	        this.isPremium = source["isPremium"];
	        this.expireTime = this.convertValues(source["expireTime"], null);
	        this.createdAt = this.convertValues(source["createdAt"], null);
	        this.updatedAt = this.convertValues(source["updatedAt"], null);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

