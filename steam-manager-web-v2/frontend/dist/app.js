// 全局变量
let currentUser = null;
let isNetworkMode = false;

// ============ 自定义提示框功能 ============

// 自定义Alert提示框
function customAlert(message, type = 'info', title = '') {
    return new Promise((resolve) => {
        showCustomModal({
            type: type,
            title: title || getDefaultTitle(type),
            message: message,
            buttons: [
                {
                    text: '确定',
                    class: 'modal-btn-primary',
                    onClick: () => {
                        hideCustomModal();
                        resolve(true);
                    }
                }
            ]
        });
    });
}

// 自定义Confirm确认框
function customConfirm(message, title = '确认操作') {
    return new Promise((resolve) => {
        showCustomModal({
            type: 'confirm',
            title: title,
            message: message,
            buttons: [
                {
                    text: '取消',
                    class: 'modal-btn-secondary',
                    onClick: () => {
                        hideCustomModal();
                        resolve(false);
                    }
                },
                {
                    text: '确定',
                    class: 'modal-btn-primary',
                    onClick: () => {
                        hideCustomModal();
                        resolve(true);
                    }
                }
            ]
        });
    });
}

// 显示自定义模态框
function showCustomModal(options) {
    const modal = document.getElementById('customModal');
    const icon = document.getElementById('modalIcon');
    const title = document.getElementById('modalTitle');
    const message = document.getElementById('modalMessage');
    const buttons = document.getElementById('modalButtons');

    // 设置图标
    icon.className = `modal-icon ${options.type}`;
    icon.textContent = getIconText(options.type);

    // 设置标题和消息
    title.textContent = options.title;
    message.textContent = options.message;

    // 清空并设置按钮
    buttons.innerHTML = '';
    options.buttons.forEach(button => {
        const btn = document.createElement('button');
        btn.className = `modal-btn ${button.class}`;
        btn.textContent = button.text;
        btn.onclick = button.onClick;
        buttons.appendChild(btn);
    });

    // 显示模态框
    modal.style.display = 'block';

    // 点击背景关闭（仅对alert类型）
    if (options.type !== 'confirm') {
        modal.onclick = (e) => {
            if (e.target === modal) {
                hideCustomModal();
            }
        };
    }
}

// 隐藏自定义模态框
function hideCustomModal() {
    const modal = document.getElementById('customModal');
    modal.style.display = 'none';
    modal.onclick = null;
}

// 获取图标文本
function getIconText(type) {
    const icons = {
        success: '✓',
        error: '✕',
        warning: '!',
        info: 'i',
        confirm: '?'
    };
    return icons[type] || 'i';
}

// 获取默认标题
function getDefaultTitle(type) {
    const titles = {
        success: '成功',
        error: '错误',
        warning: '警告',
        info: '提示',
        confirm: '确认'
    };
    return titles[type] || '提示';
}

// 重写原生alert和confirm
window.alert = customAlert;
window.confirm = customConfirm;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
async function initializeApp() {
    try {
        // 检查网络状态
        await checkNetworkStatus();
        
        // 加载账号列表
        await refreshAccounts();
        
        // 加载日志
        await refreshLogs();
        
        // 定期刷新状态
        setInterval(checkNetworkStatus, 30000); // 30秒检查一次
        setInterval(refreshLogs, 10000); // 10秒刷新一次日志
    } catch (error) {
        console.error('初始化失败:', error);
    }
}

// 检查网络状态
async function checkNetworkStatus() {
    try {
        const status = await window.go.main.App.GetNetworkStatus();
        isNetworkMode = status.isNetworkMode;
        currentUser = status.user;
        
        updateNetworkStatusUI(status);
        updateUserInfoUI(status);
    } catch (error) {
        console.error('检查网络状态失败:', error);
        updateNetworkStatusUI({ isNetworkMode: false, isLoggedIn: false });
    }
}

// 更新网络状态UI
function updateNetworkStatusUI(status) {
    const indicator = document.getElementById('statusIndicator');
    const text = document.getElementById('statusText');
    
    if (status.isNetworkMode) {
        if (status.isLoggedIn) {
            indicator.className = 'status-indicator status-online';
            text.textContent = '网络模式 - 已登录';
        } else {
            indicator.className = 'status-indicator status-offline';
            text.textContent = '网络模式 - 未登录';
        }
    } else {
        indicator.className = 'status-indicator status-offline';
        text.textContent = '本地模式';
    }
}

// 更新用户信息UI
function updateUserInfoUI(status) {
    const userInfo = document.getElementById('userInfo');
    const userDetails = document.getElementById('userDetails');
    const authMenu = document.getElementById('authMenu');
    const logoutMenu = document.getElementById('logoutMenu');
    
    if (status.isNetworkMode && status.isLoggedIn && status.user) {
        userInfo.style.display = 'block';
        userDetails.innerHTML = `
            <div><strong>${status.user.username}</strong></div>
            <div style="font-size: 12px; color: #666;">
                ${status.user.isPremium ? '高级版用户' : '免费版用户'}
            </div>
        `;
        authMenu.style.display = 'none';
        logoutMenu.style.display = 'block';
    } else {
        userInfo.style.display = 'none';
        if (status.isNetworkMode) {
            authMenu.style.display = 'block';
            logoutMenu.style.display = 'none';
        } else {
            authMenu.style.display = 'none';
            logoutMenu.style.display = 'none';
        }
    }
}

// 显示页面
function showPage(pageId) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    
    // 显示指定页面
    document.getElementById(pageId).classList.add('active');
    
    // 更新导航状态
    document.querySelectorAll('.nav-item a').forEach(link => {
        link.classList.remove('active');
    });
    event.target.classList.add('active');
}

// 刷新账号列表
async function refreshAccounts() {
    try {
        const accounts = await window.go.main.App.GetAccounts();
        displayAccounts(accounts);
    } catch (error) {
        console.error('获取账号列表失败:', error);
        document.getElementById('accountsList').innerHTML = '<p>获取账号列表失败</p>';
    }
}

// 显示账号列表
function displayAccounts(accounts) {
    const container = document.getElementById('accountsList');
    
    if (!accounts || accounts.length === 0) {
        container.innerHTML = '<p>暂无账号，点击"添加账号"开始使用</p>';
        return;
    }
    
    let html = `
        <table class="account-table">
            <thead>
                <tr>
                    <th>用户名</th>
                    <th>SteamID</th>
                    <th>状态</th>
                    <th>备注</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    accounts.forEach(account => {
        const statusClass = getStatusClass(account.status);
        html += `
            <tr>
                <td>${account.username}</td>
                <td>${account.steamId || '未获取'}</td>
                <td><span class="status-badge ${statusClass}">${account.status}</span></td>
                <td>${account.notes || '-'}</td>
                <td>
                    <button class="btn btn-primary" onclick="loginSteam('${account.username}', '${account.password}')">登录</button>
                    <button class="btn btn-secondary" onclick="deleteAccount(${account.id})">删除</button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    container.innerHTML = html;
}

// 获取状态样式类
function getStatusClass(status) {
    if (status === '正常') return 'status-normal';
    if (status.includes('封禁')) return 'status-banned';
    return 'status-unknown';
}

// 显示添加账号表单
function showAddAccountForm() {
    document.getElementById('addAccountForm').style.display = 'block';
}

// 隐藏添加账号表单
function hideAddAccountForm() {
    document.getElementById('addAccountForm').style.display = 'none';
    // 清空表单
    document.getElementById('newUsername').value = '';
    document.getElementById('newPassword').value = '';
    document.getElementById('newNotes').value = '';
}

// 添加账号
async function addAccount() {
    const username = document.getElementById('newUsername').value.trim();
    const password = document.getElementById('newPassword').value.trim();
    const notes = document.getElementById('newNotes').value.trim();
    
    if (!username || !password) {
        alert('请输入用户名和密码');
        return;
    }
    
    try {
        await window.go.main.App.AddAccount(username, password, notes);
        hideAddAccountForm();
        await refreshAccounts();
        alert('账号添加成功！');
    } catch (error) {
        alert('添加账号失败: ' + error);
    }
}

// 删除账号
async function deleteAccount(accountId) {
    if (!confirm('确定要删除这个账号吗？')) {
        return;
    }
    
    try {
        await window.go.main.App.DeleteAccount(accountId);
        await refreshAccounts();
        alert('账号删除成功！');
    } catch (error) {
        alert('删除账号失败: ' + error);
    }
}

// Steam登录
async function loginSteam(username, password) {
    try {
        await window.go.main.App.LoginSteam(username, password);
        alert('Steam登录命令已执行！');
    } catch (error) {
        alert('Steam登录失败: ' + error);
    }
}

// 用户登录
async function login() {
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value.trim();
    
    if (!username || !password) {
        alert('请输入用户名和密码');
        return;
    }
    
    try {
        await window.go.main.App.Login(username, password);
        await checkNetworkStatus();
        await refreshAccounts();
        showPage('accounts');
        alert('登录成功！');
    } catch (error) {
        alert('登录失败: ' + error);
    }
}

// 用户注册
async function register() {
    const username = document.getElementById('registerUsername').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const password = document.getElementById('registerPassword').value.trim();
    
    if (!username || !email || !password) {
        alert('请填写所有字段');
        return;
    }
    
    try {
        await window.go.main.App.Register(username, email, password);
        await checkNetworkStatus();
        await refreshAccounts();
        showPage('accounts');
        alert('注册成功！');
    } catch (error) {
        alert('注册失败: ' + error);
    }
}

// 用户登出
async function logout() {
    try {
        await window.go.main.App.Logout();
        await checkNetworkStatus();
        await refreshAccounts();
        showPage('auth');
        alert('已退出登录');
    } catch (error) {
        alert('退出登录失败: ' + error);
    }
}

// 显示注册表单
function showRegisterForm() {
    document.getElementById('loginForm').style.display = 'none';
    document.getElementById('registerForm').style.display = 'block';
}

// 显示登录表单
function showLoginForm() {
    document.getElementById('registerForm').style.display = 'none';
    document.getElementById('loginForm').style.display = 'block';
}

// 刷新日志
async function refreshLogs() {
    try {
        const logs = await window.go.main.App.GetLogs();
        displayLogs(logs);
    } catch (error) {
        console.error('获取日志失败:', error);
    }
}

// 显示日志
function displayLogs(logs) {
    const container = document.getElementById('logsContainer');
    
    if (!logs || logs.length === 0) {
        container.innerHTML = '<p>暂无日志</p>';
        return;
    }
    
    let html = '';
    logs.forEach(log => {
        html += `
            <div class="log-entry">
                <span class="log-timestamp">[${log.timestamp}]</span>
                <span class="log-level-${log.level}">[${log.level}]</span>
                <span>[${log.category}]</span>
                <span>${log.message}</span>
            </div>
        `;
    });
    
    container.innerHTML = html;
    // 滚动到底部
    container.scrollTop = container.scrollHeight;
}

// 清空日志
async function clearLogs() {
    try {
        await window.go.main.App.ClearLogs();
        await refreshLogs();
    } catch (error) {
        alert('清空日志失败: ' + error);
    }
}
