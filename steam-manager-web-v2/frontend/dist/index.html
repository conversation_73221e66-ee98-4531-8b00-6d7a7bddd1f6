<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="application-name" content="Steam账号管理工具">
    <meta name="description" content="Steam账号管理工具 - 网络版">
    <title>Steam账号管理工具 - 网络版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #667eea;
            font-size: 18px;
            margin-bottom: 5px;
        }

        .logo .version {
            color: #999;
            font-size: 12px;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 10px;
        }

        .nav-item a {
            display: block;
            padding: 12px 15px;
            color: #333;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .nav-item a:hover,
        .nav-item a.active {
            background: #667eea;
            color: white;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #4a5568;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .account-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .account-table th,
        .account-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .account-table th {
            background: #f7fafc;
            font-weight: 600;
            color: #4a5568;
        }

        .account-table tr:hover {
            background: #f7fafc;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-normal {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-banned {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-unknown {
            background: #e2e8f0;
            color: #4a5568;
        }

        .user-info {
            background: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .network-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .status-online {
            background: #48bb78;
        }

        .status-offline {
            background: #f56565;
        }

        .logs-container {
            max-height: 300px;
            overflow-y: auto;
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
        }

        .log-timestamp {
            color: #a0aec0;
        }

        .log-level-INFO {
            color: #68d391;
        }

        .log-level-ERROR {
            color: #f56565;
        }

        .log-level-WARN {
            color: #f6e05e;
        }

        .log-level-SUCCESS {
            color: #4fd1c7;
        }

        /* 自定义提示框样式 */
        .custom-modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .custom-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            min-width: 300px;
            max-width: 500px;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }

        .modal-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .modal-icon.success {
            background: #48bb78;
        }

        .modal-icon.error {
            background: #f56565;
        }

        .modal-icon.warning {
            background: #ed8936;
        }

        .modal-icon.info {
            background: #4299e1;
        }

        .modal-icon.confirm {
            background: #667eea;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
        }

        .modal-message {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            min-width: 80px;
        }

        .modal-btn-primary {
            background: #667eea;
            color: white;
        }

        .modal-btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .modal-btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .modal-btn-secondary:hover {
            background: #cbd5e0;
        }

        .modal-btn-danger {
            background: #f56565;
            color: white;
        }

        .modal-btn-danger:hover {
            background: #e53e3e;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <h1>Steam账号管理工具</h1>
                <div class="version">网络版 v2.0</div>
            </div>

            <!-- 网络状态 -->
            <div class="network-status" id="networkStatus">
                <div class="status-indicator status-offline" id="statusIndicator"></div>
                <span id="statusText">检查中...</span>
            </div>

            <!-- 用户信息 -->
            <div class="user-info" id="userInfo" style="display: none;">
                <div id="userDetails"></div>
            </div>

            <!-- 导航菜单 -->
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" onclick="showPage('accounts')" class="active">账号管理</a>
                </li>
                <li class="nav-item">
                    <a href="#" onclick="showPage('settings')">设置</a>
                </li>
                <li class="nav-item">
                    <a href="#" onclick="showPage('logs')">日志</a>
                </li>
                <li class="nav-item" id="authMenu">
                    <a href="#" onclick="showPage('auth')">登录/注册</a>
                </li>
                <li class="nav-item" id="logoutMenu" style="display: none;">
                    <a href="#" onclick="logout()">退出登录</a>
                </li>
            </ul>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 账号管理页面 -->
            <div id="accounts" class="page active">
                <div class="content-card">
                    <h2>账号管理</h2>
                    
                    <div style="margin: 20px 0;">
                        <button class="btn btn-primary" onclick="showAddAccountForm()">添加账号</button>
                        <button class="btn btn-secondary" onclick="showImportForm()">批量导入</button>
                        <button class="btn btn-secondary" onclick="refreshAccounts()">刷新</button>
                    </div>

                    <!-- 添加账号表单 -->
                    <div id="addAccountForm" style="display: none; margin-bottom: 20px;">
                        <h3>添加新账号</h3>
                        <div class="form-group">
                            <label>Steam用户名</label>
                            <input type="text" id="newUsername" placeholder="输入Steam用户名">
                        </div>
                        <div class="form-group">
                            <label>Steam密码</label>
                            <input type="password" id="newPassword" placeholder="输入Steam密码">
                        </div>
                        <div class="form-group">
                            <label>备注</label>
                            <input type="text" id="newNotes" placeholder="可选备注信息">
                        </div>
                        <button class="btn btn-primary" onclick="addAccount()">添加</button>
                        <button class="btn btn-secondary" onclick="hideAddAccountForm()">取消</button>
                    </div>

                    <!-- 账号列表 -->
                    <div id="accountsList">
                        <p>正在加载账号列表...</p>
                    </div>
                </div>
            </div>

            <!-- 设置页面 -->
            <div id="settings" class="page">
                <div class="content-card">
                    <h2>设置</h2>
                    <p>设置功能开发中...</p>
                </div>
            </div>

            <!-- 日志页面 -->
            <div id="logs" class="page">
                <div class="content-card">
                    <h2>系统日志</h2>
                    <button class="btn btn-secondary" onclick="refreshLogs()">刷新日志</button>
                    <button class="btn btn-secondary" onclick="clearLogs()">清空日志</button>
                    <div class="logs-container" id="logsContainer">
                        <p>正在加载日志...</p>
                    </div>
                </div>
            </div>

            <!-- 登录/注册页面 -->
            <div id="auth" class="page">
                <div class="content-card">
                    <h2>用户认证</h2>
                    
                    <!-- 登录表单 -->
                    <div id="loginForm">
                        <h3>登录</h3>
                        <div class="form-group">
                            <label>用户名</label>
                            <input type="text" id="loginUsername" placeholder="输入用户名">
                        </div>
                        <div class="form-group">
                            <label>密码</label>
                            <input type="password" id="loginPassword" placeholder="输入密码">
                        </div>
                        <button class="btn btn-primary" onclick="login()">登录</button>
                        <button class="btn btn-secondary" onclick="showRegisterForm()">注册新账号</button>
                    </div>

                    <!-- 注册表单 -->
                    <div id="registerForm" style="display: none;">
                        <h3>注册</h3>
                        <div class="form-group">
                            <label>用户名</label>
                            <input type="text" id="registerUsername" placeholder="输入用户名">
                        </div>
                        <div class="form-group">
                            <label>邮箱</label>
                            <input type="email" id="registerEmail" placeholder="输入邮箱地址">
                        </div>
                        <div class="form-group">
                            <label>密码</label>
                            <input type="password" id="registerPassword" placeholder="输入密码">
                        </div>
                        <button class="btn btn-primary" onclick="register()">注册</button>
                        <button class="btn btn-secondary" onclick="showLoginForm()">返回登录</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义提示框 -->
    <div id="customModal" class="custom-modal">
        <div class="custom-modal-content">
            <div class="modal-header">
                <div id="modalIcon" class="modal-icon"></div>
                <div id="modalTitle" class="modal-title"></div>
            </div>
            <div id="modalMessage" class="modal-message"></div>
            <div id="modalButtons" class="modal-buttons"></div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
