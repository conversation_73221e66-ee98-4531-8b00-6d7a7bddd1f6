package main

import (
	"log"
	"time"
)

// addLog 添加日志条目 - 扩展支持网络版日志
func (a *App) addLog(level, message, category string) {
	a.logMutex.Lock()
	defer a.logMutex.Unlock()

	logEntry := LogEntry{
		Timestamp: time.Now().Format("15:04:05"),
		Level:     level,
		Message:   message,
		Category:  category,
	}

	// 添加到日志列表开头
	a.logs = append([]LogEntry{logEntry}, a.logs...)

	// 保持最多100条日志
	if len(a.logs) > 100 {
		a.logs = a.logs[:100]
	}

	// 同时输出到控制台
	log.Printf("[%s] %s: %s", category, level, message)
}

// GetLogs 获取日志列表
func (a *App) GetLogs() []LogEntry {
	a.logMutex.RLock()
	defer a.logMutex.RUnlock()

	// 返回日志副本
	logs := make([]LogEntry, len(a.logs))
	copy(logs, a.logs)
	return logs
}

// ClearLogs 清空日志
func (a *App) ClearLogs() {
	a.logMutex.Lock()
	defer a.logMutex.Unlock()
	a.logs = make([]LogEntry, 0)

	// 不在这里添加日志，避免清空后立即又有日志
	log.Printf("日志已清空")
}
