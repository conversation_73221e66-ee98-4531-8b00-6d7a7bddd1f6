package main

import (
	"fmt"
	"os/exec"
	"strings"
	"syscall"
)

// executeCommand 执行系统命令（隐藏窗口）
func (a *App) executeCommand(name string, args ...string) (string, error) {
	cmd := exec.Command(name, args...)

	// 在Windows上隐藏命令行窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("命令执行失败: %s %v - %v", name, args, err), "general")
		return string(output), err
	}

	result := strings.TrimSpace(string(output))
	a.addLog("INFO", fmt.Sprintf("命令执行成功: %s %v", name, args), "general")
	return result, nil
}

// runPowerShellCommand 运行PowerShell命令（隐藏窗口）
func (a *App) runPowerShellCommand(command string) (string, error) {
	cmd := exec.Command("powershell", "-Command", command)

	// 在Windows上隐藏命令行窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("PowerShell命令执行失败: %s - %v", command, err), "general")
		return string(output), err
	}

	result := strings.TrimSpace(string(output))
	a.addLog("INFO", fmt.Sprintf("PowerShell命令执行成功: %s", command), "general")
	return result, nil
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, 欢迎使用Steam账号管理工具 - 网络版!", name)
}
