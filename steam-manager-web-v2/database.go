package main

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// initializeDatabase 初始化数据库 - 扩展支持MySQL
func (a *App) initializeDatabase() error {
	var err error
	
	if a.isNetworkMode {
		// 网络模式：使用MySQL
		err = a.initializeMySQLDatabase()
	} else {
		// 本地模式：使用SQLite
		err = a.initializeSQLiteDatabase()
	}
	
	if err != nil {
		return err
	}

	// 创建表结构
	return a.createTables()
}

// initializeMySQLDatabase 初始化MySQL数据库
func (a *App) initializeMySQLDatabase() error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		a.config.Network.Database.Username,
		a.config.Network.Database.Password,
		a.config.Network.Database.Host,
		a.config.Network.Database.Port,
		a.config.Network.Database.Database,
	)

	var err error
	a.db, err = sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接MySQL数据库失败: %v", err)
	}

	// 测试连接
	if err = a.db.Ping(); err != nil {
		return fmt.Errorf("MySQL数据库连接测试失败: %v", err)
	}

	a.addLog("INFO", "MySQL数据库连接成功", "database")
	return nil
}

// initializeSQLiteDatabase 初始化SQLite数据库
func (a *App) initializeSQLiteDatabase() error {
	// 确保数据目录存在
	dbPath := a.config.Database.Path
	dbDir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %v", err)
	}

	var err error
	a.db, err = sql.Open("sqlite", dbPath)
	if err != nil {
		return fmt.Errorf("打开SQLite数据库失败: %v", err)
	}

	// 启用外键约束
	if _, err = a.db.Exec("PRAGMA foreign_keys = ON"); err != nil {
		return fmt.Errorf("启用外键约束失败: %v", err)
	}

	a.addLog("INFO", "SQLite数据库连接成功", "database")
	return nil
}

// createTables 创建数据表 - 扩展支持用户和订阅表
func (a *App) createTables() error {
	var queries []string

	if a.isNetworkMode {
		// MySQL表结构
		queries = []string{
			// 用户表
			`CREATE TABLE IF NOT EXISTS users (
				id INT AUTO_INCREMENT PRIMARY KEY,
				username VARCHAR(50) UNIQUE NOT NULL,
				email VARCHAR(100) UNIQUE NOT NULL,
				password VARCHAR(255) NOT NULL,
				is_premium BOOLEAN DEFAULT FALSE,
				expire_time DATETIME NULL,
				created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
				updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
			
			// 账号表
			`CREATE TABLE IF NOT EXISTS accounts (
				id INT AUTO_INCREMENT PRIMARY KEY,
				user_id INT NOT NULL,
				username VARCHAR(100) NOT NULL,
				password VARCHAR(255) NOT NULL,
				notes TEXT,
				status VARCHAR(20) DEFAULT 'unknown',
				steam_id VARCHAR(20) DEFAULT '',
				pubg_ban_status INT DEFAULT -1,
				last_login DATETIME NULL,
				last_checked DATETIME NULL,
				created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
				updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
				FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
				UNIQUE KEY unique_user_account (user_id, username)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
			
			// 订阅表
			`CREATE TABLE IF NOT EXISTS subscriptions (
				id INT AUTO_INCREMENT PRIMARY KEY,
				user_id INT NOT NULL,
				type VARCHAR(20) DEFAULT 'free',
				start_time DATETIME NOT NULL,
				end_time DATETIME NOT NULL,
				is_active BOOLEAN DEFAULT TRUE,
				created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
				updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
				FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
			
			// 订单表
			`CREATE TABLE IF NOT EXISTS orders (
				id INT AUTO_INCREMENT PRIMARY KEY,
				user_id INT NOT NULL,
				order_no VARCHAR(50) UNIQUE NOT NULL,
				amount DECIMAL(10,2) NOT NULL,
				status VARCHAR(20) DEFAULT 'pending',
				pay_method VARCHAR(20) NOT NULL,
				description TEXT,
				created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
				updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
				FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
		}
	} else {
		// SQLite表结构（保持原有结构）
		queries = []string{
			`CREATE TABLE IF NOT EXISTS accounts (
				id INTEGER PRIMARY KEY AUTOINCREMENT,
				username TEXT NOT NULL UNIQUE,
				password TEXT NOT NULL,
				notes TEXT DEFAULT '',
				status TEXT DEFAULT 'unknown',
				steam_id TEXT DEFAULT '',
				pubg_ban_status INTEGER DEFAULT -1,
				last_login DATETIME,
				last_checked DATETIME,
				created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
				updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
			)`,
		}
	}

	// 执行创建表的SQL
	for _, query := range queries {
		if _, err := a.db.Exec(query); err != nil {
			return fmt.Errorf("创建表失败: %v", err)
		}
	}

	a.addLog("INFO", "数据表创建成功", "database")
	return nil
}

// loadAccountsFromDB 从数据库加载账号 - 扩展支持用户过滤
func (a *App) loadAccountsFromDB() {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	var query string
	var args []interface{}

	if a.isNetworkMode && a.currentUser != nil {
		// 网络模式：只加载当前用户的账号
		query = `SELECT id, username, password, notes, status, steam_id, pubg_ban_status, 
				last_login, last_checked, created_at, updated_at 
				FROM accounts WHERE user_id = ? ORDER BY id`
		args = []interface{}{a.currentUser.ID}
	} else {
		// 本地模式：加载所有账号
		query = `SELECT id, username, password, notes, status, steam_id, pubg_ban_status, 
				last_login, last_checked, created_at, updated_at 
				FROM accounts ORDER BY id`
		args = []interface{}{}
	}

	rows, err := a.db.Query(query, args...)
	if err != nil {
		a.addLog("ERROR", "从数据库加载账号失败: "+err.Error(), "database")
		return
	}
	defer rows.Close()

	a.accounts = []Account{}
	maxID := 0

	for rows.Next() {
		var account Account
		var lastLogin, lastChecked sql.NullTime

		err := rows.Scan(
			&account.ID,
			&account.Username,
			&account.Password,
			&account.Notes,
			&account.Status,
			&account.SteamID,
			&account.PUBGBanStatus,
			&lastLogin,
			&lastChecked,
			&account.CreatedAt,
			&account.UpdatedAt,
		)

		if err != nil {
			a.addLog("ERROR", "扫描账号数据失败: "+err.Error(), "database")
			continue
		}

		// 处理可能为NULL的时间字段
		if lastLogin.Valid {
			account.LastLogin = lastLogin.Time
		}
		if lastChecked.Valid {
			account.LastChecked = lastChecked.Time
		}

		// 设置用户ID（网络模式）
		if a.isNetworkMode && a.currentUser != nil {
			account.UserID = a.currentUser.ID
		}

		a.accounts = append(a.accounts, account)

		if account.ID > maxID {
			maxID = account.ID
		}
	}

	a.nextID = maxID + 1
	a.addLog("INFO", fmt.Sprintf("从数据库加载了 %d 个账号", len(a.accounts)), "database")
}

// ============ 网络版新增数据库方法 ============

// authenticateUser 用户认证
func (a *App) authenticateUser(username, password string) (*User, error) {
	if !a.isNetworkMode {
		return nil, fmt.Errorf("本地模式不支持用户认证")
	}

	var user User
	var hashedPassword string
	
	query := `SELECT id, username, email, password, is_premium, expire_time, created_at, updated_at 
			  FROM users WHERE username = ?`
	
	err := a.db.QueryRow(query, username).Scan(
		&user.ID, &user.Username, &user.Email, &hashedPassword,
		&user.IsPremium, &user.ExpireTime, &user.CreatedAt, &user.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, fmt.Errorf("用户名或密码错误")
	}
	if err != nil {
		return nil, fmt.Errorf("数据库查询失败: %v", err)
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password)); err != nil {
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 检查会员是否过期
	if user.IsPremium && user.ExpireTime != nil && time.Now().After(*user.ExpireTime) {
		user.IsPremium = false
		// 更新数据库
		a.db.Exec("UPDATE users SET is_premium = FALSE WHERE id = ?", user.ID)
	}

	return &user, nil
}

// createUser 创建用户
func (a *App) createUser(username, email, password string) (*User, error) {
	if !a.isNetworkMode {
		return nil, fmt.Errorf("本地模式不支持用户注册")
	}

	// 检查用户名是否已存在
	var count int
	err := a.db.QueryRow("SELECT COUNT(*) FROM users WHERE username = ?", username).Scan(&count)
	if err != nil {
		return nil, fmt.Errorf("数据库查询失败: %v", err)
	}
	if count > 0 {
		return nil, fmt.Errorf("用户名已存在")
	}

	// 检查邮箱是否已存在
	err = a.db.QueryRow("SELECT COUNT(*) FROM users WHERE email = ?", email).Scan(&count)
	if err != nil {
		return nil, fmt.Errorf("数据库查询失败: %v", err)
	}
	if count > 0 {
		return nil, fmt.Errorf("邮箱已存在")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %v", err)
	}

	// 创建用户
	now := time.Now()
	result, err := a.db.Exec(
		"INSERT INTO users (username, email, password, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
		username, email, string(hashedPassword), now, now,
	)
	if err != nil {
		return nil, fmt.Errorf("创建用户失败: %v", err)
	}

	userID, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("获取用户ID失败: %v", err)
	}

	user := &User{
		ID:        int(userID),
		Username:  username,
		Email:     email,
		IsPremium: false,
		CreatedAt: now,
		UpdatedAt: now,
	}

	return user, nil
}

// getUserSubscription 获取用户订阅信息
func (a *App) getUserSubscription(userID int) (*Subscription, error) {
	if !a.isNetworkMode {
		return nil, fmt.Errorf("本地模式不支持订阅")
	}

	var subscription Subscription
	query := `SELECT id, user_id, type, start_time, end_time, is_active, created_at, updated_at 
			  FROM subscriptions WHERE user_id = ? AND is_active = TRUE ORDER BY created_at DESC LIMIT 1`
	
	err := a.db.QueryRow(query, userID).Scan(
		&subscription.ID, &subscription.UserID, &subscription.Type,
		&subscription.StartTime, &subscription.EndTime, &subscription.IsActive,
		&subscription.CreatedAt, &subscription.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil // 没有订阅
	}
	if err != nil {
		return nil, fmt.Errorf("查询订阅失败: %v", err)
	}

	return &subscription, nil
}

// upgradeToPremium 升级到高级版
func (a *App) upgradeToPremium(userID int) error {
	if !a.isNetworkMode {
		return fmt.Errorf("本地模式不支持升级")
	}

	// 开始事务
	tx, err := a.db.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}
	defer tx.Rollback()

	// 设置会员到期时间为一年后
	expireTime := time.Now().AddDate(1, 0, 0)
	
	// 更新用户状态
	_, err = tx.Exec("UPDATE users SET is_premium = TRUE, expire_time = ? WHERE id = ?", expireTime, userID)
	if err != nil {
		return fmt.Errorf("更新用户状态失败: %v", err)
	}

	// 创建订阅记录
	now := time.Now()
	_, err = tx.Exec(
		"INSERT INTO subscriptions (user_id, type, start_time, end_time, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
		userID, "premium", now, expireTime, true, now, now,
	)
	if err != nil {
		return fmt.Errorf("创建订阅记录失败: %v", err)
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	// 更新当前用户信息
	if a.currentUser != nil && a.currentUser.ID == userID {
		a.userMutex.Lock()
		a.currentUser.IsPremium = true
		a.currentUser.ExpireTime = &expireTime
		a.userMutex.Unlock()
	}

	return nil
}
