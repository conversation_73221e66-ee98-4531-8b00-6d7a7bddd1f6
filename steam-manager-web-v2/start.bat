@echo off
echo Starting Steam Manager Web v2...
echo.

REM 检查Wails是否安装
wails version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Wails is not installed
    echo Please install Wails: go install github.com/wailsapp/wails/v2/cmd/wails@latest
    pause
    exit /b 1
)

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go is not installed
    echo Please install Go 1.21+ from https://golang.org/
    pause
    exit /b 1
)

REM 安装依赖
echo Installing dependencies...
go mod tidy

REM 启动开发服务器
echo Starting development server...
wails dev

pause
