package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql"
	_ "modernc.org/sqlite"
)

// App struct - 扩展原有App结构，增加网络功能
type App struct {
	ctx         context.Context
	db          *sql.DB
	config      *Config
	accounts    []Account
	mutex       sync.RWMutex
	nextID      int
	apiKeyIndex int
	logs        []LogEntry
	logMutex    sync.RWMutex
	
	// 网络版新增字段
	currentUser    *User      // 当前登录用户
	isNetworkMode  bool       // 是否为网络模式
	userMutex      sync.RWMutex
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{
		accounts: make([]Account, 0),
		nextID:   1,
		logs:     make([]LogEntry, 0),
	}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx

	// 初始化配置
	if err := a.initializeConfig(); err != nil {
		log.Printf("Failed to initialize config: %v", err)
	}

	// 检测是否为网络模式（根据配置中是否有MySQL配置）
	a.isNetworkMode = a.config.Network.Enabled

	// 初始化数据库
	if err := a.initializeDatabase(); err != nil {
		log.Printf("Failed to initialize database: %v", err)
		// 如果数据库初始化失败，使用内存存储
		a.accounts = []Account{}
		a.nextID = 1
		a.isNetworkMode = false // 降级到本地模式
	} else {
		// 从数据库加载账号
		a.loadAccountsFromDB()
	}

	// 网络模式需要用户登录
	if a.isNetworkMode {
		a.addLog("INFO", "网络模式启动，需要用户登录", "network")
	} else {
		a.addLog("INFO", "本地模式启动", "local")
	}

	// 启动后台任务
	go a.startBackgroundTasks()

	// 启动时检查并获取缺失的SteamID，完成后检测封禁状态
	go a.startupSteamIDAndBanCheck()
}

// startBackgroundTasks 启动后台任务
func (a *App) startBackgroundTasks() {
	// 每30分钟检查一次缺失的SteamID
	steamIDTicker := time.NewTicker(30 * time.Minute)
	defer steamIDTicker.Stop()

	// 每1小时检查一次封禁状态（检查1小时内未检测的账号）
	banCheckTicker := time.NewTicker(1 * time.Hour)
	defer banCheckTicker.Stop()

	for {
		select {
		case <-steamIDTicker.C:
			go a.checkAndUpdateMissingSteamIDs()
		case <-banCheckTicker.C:
			a.addLog("INFO", "定时任务：开始检查1小时内未检测的账号封禁状态", "scheduler")
			go a.checkBanStatusBackground()
		case <-a.ctx.Done():
			return
		}
	}
}

// startupSteamIDAndBanCheck 启动时获取SteamID并检测封禁状态
func (a *App) startupSteamIDAndBanCheck() {
	a.addLog("INFO", "程序启动：开始检查缺失的SteamID", "startup")

	// 先获取缺失的SteamID
	a.checkAndUpdateMissingSteamIDs()

	// 等待SteamID获取完成（给一些时间让SteamID获取任务完成）
	time.Sleep(5 * time.Second)

	// 检查是否有Steam API密钥配置
	if len(a.config.API.SteamAPIKeys) == 0 {
		a.addLog("WARN", "未配置Steam API密钥，跳过封禁状态检测", "startup")
		return
	}

	a.addLog("INFO", "SteamID获取完成，开始检测1小时内未检测的账号封禁状态", "startup")

	// 启动封禁状态检测
	a.checkBanStatusOnStartup()
}

// ============ 网络版新增方法 ============

// IsNetworkMode 检查是否为网络模式
func (a *App) IsNetworkMode() bool {
	return a.isNetworkMode
}

// GetCurrentUser 获取当前登录用户
func (a *App) GetCurrentUser() *User {
	a.userMutex.RLock()
	defer a.userMutex.RUnlock()
	return a.currentUser
}

// IsLoggedIn 检查是否已登录
func (a *App) IsLoggedIn() bool {
	a.userMutex.RLock()
	defer a.userMutex.RUnlock()
	return a.currentUser != nil
}

// Login 用户登录
func (a *App) Login(username, password string) (*User, error) {
	if !a.isNetworkMode {
		return nil, fmt.Errorf("本地模式不需要登录")
	}

	user, err := a.authenticateUser(username, password)
	if err != nil {
		return nil, err
	}

	a.userMutex.Lock()
	a.currentUser = user
	a.userMutex.Unlock()

	a.addLog("INFO", fmt.Sprintf("用户 %s 登录成功", username), "auth")
	
	// 重新加载该用户的账号
	a.loadAccountsFromDB()
	
	return user, nil
}

// Register 用户注册
func (a *App) Register(username, email, password string) (*User, error) {
	if !a.isNetworkMode {
		return nil, fmt.Errorf("本地模式不支持注册")
	}

	user, err := a.createUser(username, email, password)
	if err != nil {
		return nil, err
	}

	a.userMutex.Lock()
	a.currentUser = user
	a.userMutex.Unlock()

	a.addLog("INFO", fmt.Sprintf("用户 %s 注册成功", username), "auth")
	
	return user, nil
}

// Logout 用户登出
func (a *App) Logout() {
	if !a.isNetworkMode {
		return
	}

	a.userMutex.Lock()
	username := ""
	if a.currentUser != nil {
		username = a.currentUser.Username
	}
	a.currentUser = nil
	a.userMutex.Unlock()

	// 清空账号列表
	a.mutex.Lock()
	a.accounts = []Account{}
	a.mutex.Unlock()

	if username != "" {
		a.addLog("INFO", fmt.Sprintf("用户 %s 已登出", username), "auth")
	}
}

// GetUserSubscription 获取用户订阅信息
func (a *App) GetUserSubscription() (*Subscription, error) {
	if !a.isNetworkMode || a.currentUser == nil {
		return nil, fmt.Errorf("需要登录网络版")
	}

	return a.getUserSubscription(a.currentUser.ID)
}

// UpgradeToPremium 升级到高级版
func (a *App) UpgradeToPremium() error {
	if !a.isNetworkMode || a.currentUser == nil {
		return fmt.Errorf("需要登录网络版")
	}

	return a.upgradeToPremium(a.currentUser.ID)
}

// GetNetworkStatus 获取网络状态信息
func (a *App) GetNetworkStatus() map[string]interface{} {
	status := map[string]interface{}{
		"isNetworkMode": a.isNetworkMode,
		"isLoggedIn":    a.IsLoggedIn(),
		"user":          nil,
	}

	if a.currentUser != nil {
		status["user"] = map[string]interface{}{
			"id":         a.currentUser.ID,
			"username":   a.currentUser.Username,
			"email":      a.currentUser.Email,
			"isPremium":  a.currentUser.IsPremium,
			"expireTime": a.currentUser.ExpireTime,
		}
	}

	return status
}

// ============ 缺失的方法实现 ============

// checkAndUpdateMissingSteamIDs 检查并更新缺失的SteamID
func (a *App) checkAndUpdateMissingSteamIDs() {
	a.addLog("INFO", "开始检查缺失的SteamID", "steamid")
	// TODO: 实现SteamID获取逻辑
}

// checkBanStatusBackground 后台检查封禁状态
func (a *App) checkBanStatusBackground() {
	a.addLog("INFO", "开始后台检查封禁状态", "bancheck")
	// TODO: 实现封禁状态检查逻辑
}

// checkBanStatusOnStartup 启动时检查封禁状态
func (a *App) checkBanStatusOnStartup() {
	a.addLog("INFO", "启动时检查封禁状态", "bancheck")
	// TODO: 实现启动时封禁状态检查逻辑
}

// fetchSteamIDBackground 后台获取SteamID
func (a *App) fetchSteamIDBackground(username, password string) {
	a.addLog("INFO", fmt.Sprintf("开始获取账号 %s 的SteamID", username), "steamid")
	// TODO: 实现SteamID获取逻辑
}

// checkSingleAccountBan 检查单个账号的封禁状态
func (a *App) checkSingleAccountBan(accountID int, steamID string) {
	a.addLog("INFO", fmt.Sprintf("检查账号 %d 的封禁状态", accountID), "bancheck")
	// TODO: 实现单个账号封禁检查逻辑
}
