package database

import (
	"database/sql"
	"fmt"
	"log"
	
	_ "github.com/go-sql-driver/mysql"
	"steam-manager-web/config"
)

var DB *sql.DB

// InitDB 初始化数据库连接
func InitDB() error {
	var err error
	DB, err = sql.Open("mysql", config.AppConfig.GetDSN())
	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	// 测试连接
	if err = DB.Ping(); err != nil {
		DB = nil // 设置为nil，表示没有数据库连接
		return fmt.Errorf("failed to ping database: %v", err)
	}

	// 设置连接池参数
	DB.SetMaxOpenConns(100)
	DB.SetMaxIdleConns(10)

	log.Println("Database connected successfully")
	return nil
}

// CreateTables 创建数据表
func CreateTables() error {
	// 用户表
	userTable := `
	CREATE TABLE IF NOT EXISTS users (
		id INT AUTO_INCREMENT PRIMARY KEY,
		username VARCHAR(50) UNIQUE NOT NULL,
		email VARCHAR(100) UNIQUE NOT NULL,
		password VARCHAR(255) NOT NULL,
		is_premium BOOLEAN DEFAULT FALSE,
		expire_time DATETIME NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		INDEX idx_username (username),
		INDEX idx_email (email)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`
	
	// Steam账号表
	accountTable := `
	CREATE TABLE IF NOT EXISTS steam_accounts (
		id INT AUTO_INCREMENT PRIMARY KEY,
		user_id INT NOT NULL,
		username VARCHAR(100) NOT NULL,
		password VARCHAR(255) NOT NULL,
		steam_id VARCHAR(20) DEFAULT '',
		pubg_ban_status INT DEFAULT -1,
		last_login DATETIME NULL,
		last_checked DATETIME NULL,
		notes TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
		INDEX idx_user_id (user_id),
		INDEX idx_steam_id (steam_id),
		UNIQUE KEY unique_user_account (user_id, username)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`
	
	// 订单表
	orderTable := `
	CREATE TABLE IF NOT EXISTS orders (
		id INT AUTO_INCREMENT PRIMARY KEY,
		user_id INT NOT NULL,
		order_no VARCHAR(50) UNIQUE NOT NULL,
		amount DECIMAL(10,2) NOT NULL,
		status INT DEFAULT 0,
		pay_method VARCHAR(20) NOT NULL,
		pay_time DATETIME NULL,
		expire_time DATETIME NOT NULL,
		description TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
		INDEX idx_user_id (user_id),
		INDEX idx_order_no (order_no),
		INDEX idx_status (status)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`
	
	// 订阅表
	subscriptionTable := `
	CREATE TABLE IF NOT EXISTS subscriptions (
		id INT AUTO_INCREMENT PRIMARY KEY,
		user_id INT NOT NULL,
		start_time DATETIME NOT NULL,
		end_time DATETIME NOT NULL,
		is_active BOOLEAN DEFAULT TRUE,
		order_id INT NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
		FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
		INDEX idx_user_id (user_id),
		INDEX idx_active (is_active),
		INDEX idx_end_time (end_time)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`
	
	tables := []string{userTable, accountTable, orderTable, subscriptionTable}
	
	for _, table := range tables {
		if _, err := DB.Exec(table); err != nil {
			return fmt.Errorf("failed to create table: %v", err)
		}
	}
	
	log.Println("Database tables created successfully")
	return nil
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}
