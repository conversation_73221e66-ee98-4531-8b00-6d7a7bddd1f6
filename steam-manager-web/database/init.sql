-- Steam账号管理工具数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS steam_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE steam_manager;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    is_premium BOOLEAN DEFAULT FALSE COMMENT '是否为付费用户',
    expire_time DATETIME NULL COMMENT '会员过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_premium (is_premium),
    INDEX idx_expire_time (expire_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- Steam账号表
CREATE TABLE IF NOT EXISTS steam_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    username VARCHAR(100) NOT NULL COMMENT 'Steam用户名',
    password VARCHAR(255) NOT NULL COMMENT 'Steam密码',
    steam_id VARCHAR(20) DEFAULT '' COMMENT 'SteamID',
    pubg_ban_status INT DEFAULT -1 COMMENT '封禁状态：-1未知，0正常，1VAC封禁，2游戏封禁',
    last_login DATETIME NULL COMMENT '最后登录时间',
    last_checked DATETIME NULL COMMENT '最后检查时间',
    notes TEXT COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_steam_id (steam_id),
    INDEX idx_ban_status (pubg_ban_status),
    INDEX idx_last_login (last_login),
    INDEX idx_last_checked (last_checked),
    UNIQUE KEY unique_user_account (user_id, username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Steam账号表';

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    order_no VARCHAR(50) UNIQUE NOT NULL COMMENT '订单号',
    amount DECIMAL(10,2) NOT NULL COMMENT '金额',
    status INT DEFAULT 0 COMMENT '订单状态：0待支付，1已支付，2已取消，3已退款',
    pay_method VARCHAR(20) NOT NULL COMMENT '支付方式',
    pay_time DATETIME NULL COMMENT '支付时间',
    expire_time DATETIME NOT NULL COMMENT '订单过期时间',
    description TEXT COMMENT '订单描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_order_no (order_no),
    INDEX idx_status (status),
    INDEX idx_pay_time (pay_time),
    INDEX idx_expire_time (expire_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 订阅表
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    start_time DATETIME NOT NULL COMMENT '订阅开始时间',
    end_time DATETIME NOT NULL COMMENT '订阅结束时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    order_id INT NOT NULL COMMENT '关联订单ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_active (is_active),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_order_id (order_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订阅表';

-- 系统日志表（可选）
CREATE TABLE IF NOT EXISTS system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 插入示例数据（可选）
-- INSERT INTO users (username, email, password, is_premium) VALUES 
-- ('admin', '<EMAIL>', '$2a$10$example_hash', TRUE),
-- ('demo', '<EMAIL>', '$2a$10$example_hash', FALSE);

-- 创建视图：用户统计
CREATE OR REPLACE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.is_premium,
    u.expire_time,
    COUNT(sa.id) as account_count,
    COUNT(CASE WHEN sa.pubg_ban_status = 0 THEN 1 END) as normal_accounts,
    COUNT(CASE WHEN sa.pubg_ban_status = 1 THEN 1 END) as vac_banned_accounts,
    COUNT(CASE WHEN sa.pubg_ban_status = 2 THEN 1 END) as game_banned_accounts,
    COUNT(CASE WHEN sa.pubg_ban_status = -1 THEN 1 END) as unknown_accounts,
    u.created_at,
    u.updated_at
FROM users u
LEFT JOIN steam_accounts sa ON u.id = sa.user_id
GROUP BY u.id;

-- 创建存储过程：清理过期订单
DELIMITER //
CREATE PROCEDURE CleanExpiredOrders()
BEGIN
    UPDATE orders 
    SET status = 2, updated_at = NOW() 
    WHERE status = 0 AND expire_time < NOW();
END //
DELIMITER ;

-- 创建存储过程：更新用户会员状态
DELIMITER //
CREATE PROCEDURE UpdateUserPremiumStatus()
BEGIN
    -- 更新过期的会员状态
    UPDATE users u
    LEFT JOIN subscriptions s ON u.id = s.user_id AND s.is_active = 1
    SET u.is_premium = FALSE, u.expire_time = NULL, u.updated_at = NOW()
    WHERE u.is_premium = TRUE AND (s.end_time IS NULL OR s.end_time < NOW());
    
    -- 停用过期的订阅
    UPDATE subscriptions 
    SET is_active = FALSE, updated_at = NOW()
    WHERE is_active = TRUE AND end_time < NOW();
END //
DELIMITER ;

-- 创建事件调度器（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;

-- 每小时清理过期订单
-- CREATE EVENT IF NOT EXISTS clean_expired_orders
-- ON SCHEDULE EVERY 1 HOUR
-- DO CALL CleanExpiredOrders();

-- 每天更新用户会员状态
-- CREATE EVENT IF NOT EXISTS update_premium_status
-- ON SCHEDULE EVERY 1 DAY
-- DO CALL UpdateUserPremiumStatus();

-- 显示表结构
SHOW TABLES;
DESCRIBE users;
DESCRIBE steam_accounts;
DESCRIBE orders;
DESCRIBE subscriptions;
