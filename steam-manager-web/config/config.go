package config

import (
	"encoding/json"
	"os"
)

// Config 应用配置
type Config struct {
	Server struct {
		Port string `json:"port"`
		Mode string `json:"mode"` // debug, release
	} `json:"server"`
	
	Database struct {
		Host     string `json:"host"`
		Port     string `json:"port"`
		Username string `json:"username"`
		Password string `json:"password"`
		Database string `json:"database"`
		Charset  string `json:"charset"`
	} `json:"database"`
	
	JWT struct {
		Secret     string `json:"secret"`
		ExpireHour int    `json:"expire_hour"`
	} `json:"jwt"`
	
	Steam struct {
		APIKeys []string `json:"api_keys"`
	} `json:"steam"`
	
	Payment struct {
		YearlyPrice float64 `json:"yearly_price"` // 年费价格
	} `json:"payment"`
}

var AppConfig *Config

// LoadConfig 加载配置文件
func LoadConfig(configPath string) error {
	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		if err := createDefaultConfig(configPath); err != nil {
			return err
		}
	}
	
	file, err := os.Open(configPath)
	if err != nil {
		return err
	}
	defer file.Close()
	
	decoder := json.NewDecoder(file)
	AppConfig = &Config{}
	return decoder.Decode(AppConfig)
}

// createDefaultConfig 创建默认配置文件
func createDefaultConfig(configPath string) error {
	defaultConfig := &Config{}
	
	// 服务器配置
	defaultConfig.Server.Port = "8080"
	defaultConfig.Server.Mode = "debug"
	
	// 数据库配置
	defaultConfig.Database.Host = "localhost"
	defaultConfig.Database.Port = "3306"
	defaultConfig.Database.Username = "root"
	defaultConfig.Database.Password = "password"
	defaultConfig.Database.Database = "steam_manager"
	defaultConfig.Database.Charset = "utf8mb4"
	
	// JWT配置
	defaultConfig.JWT.Secret = "your-secret-key-change-this-in-production"
	defaultConfig.JWT.ExpireHour = 24 * 7 // 7天
	
	// Steam API配置
	defaultConfig.Steam.APIKeys = []string{
		"6450F125515588614814C4A636002A51",
		"5EB306084E5CB78D76E3DDFBF03346A7",
		"C1FE80472F4FA401E9BF38E195EB8677",
		"F1C8B59D43BAC59F6B648FD0D217B974",
		"738D637E98D73D27B9802CA833784D7F",
		"A862570CC2139926066420C4E9A5A927",
	}
	
	// 支付配置
	defaultConfig.Payment.YearlyPrice = 20.0
	
	file, err := os.Create(configPath)
	if err != nil {
		return err
	}
	defer file.Close()
	
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	return encoder.Encode(defaultConfig)
}

// GetDSN 获取数据库连接字符串
func (c *Config) GetDSN() string {
	return c.Database.Username + ":" + c.Database.Password + "@tcp(" + 
		   c.Database.Host + ":" + c.Database.Port + ")/" + 
		   c.Database.Database + "?charset=" + c.Database.Charset + "&parseTime=True&loc=Local"
}
