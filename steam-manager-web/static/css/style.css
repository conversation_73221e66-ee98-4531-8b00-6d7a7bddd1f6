/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* 导航栏 */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    font-size: 1.5rem;
    margin-bottom: 0.2rem;
}

.nav-brand .version {
    font-size: 0.8rem;
    opacity: 0.8;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-item {
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
    position: relative;
}

.nav-item:hover {
    background-color: rgba(255,255,255,0.1);
}

/* 下拉菜单 */
.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    color: #333;
    min-width: 150px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 5px;
    display: none;
    z-index: 1000;
}

.nav-item:hover .dropdown {
    display: block;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* 主要内容 */
.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

/* 页面切换 */
.page {
    display: none;
}

.page.active {
    display: block;
}

/* 欢迎页面 */
.welcome-container {
    text-align: center;
    padding: 2rem 0;
}

.welcome-container h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #667eea;
}

/* 定价卡片 */
.pricing-section {
    margin-top: 4rem;
}

.pricing-section h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.pricing-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.pricing-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.pricing-card.premium {
    border: 3px solid #667eea;
    transform: scale(1.05);
}

.pricing-card h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.price {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 1.5rem;
}

.price span {
    font-size: 1rem;
    color: #666;
}

.pricing-card ul {
    list-style: none;
    margin-bottom: 2rem;
}

.pricing-card li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

/* 认证表单 */
.auth-container {
    max-width: 400px;
    margin: 2rem auto;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.auth-container h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #555;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.auth-switch {
    text-align: center;
    margin-top: 1.5rem;
    color: #666;
}

.auth-switch a {
    color: #667eea;
    text-decoration: none;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* 按钮 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eee;
}

.page-header h2 {
    color: #333;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

/* 统计卡片 */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.stat-label {
    color: #666;
    margin-top: 0.5rem;
}

/* 表格 */
.accounts-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.table tr:hover {
    background: #f8f9fa;
}

/* 状态标签 */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-normal {
    background: #d4edda;
    color: #155724;
}

.status-banned {
    background: #f8d7da;
    color: #721c24;
}

.status-unknown {
    background: #e2e3e5;
    color: #383d41;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination button {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.3s;
}

.pagination button:hover {
    background: #f8f9fa;
}

.pagination button.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: #333;
    color: white;
    padding: 1rem 2rem;
    border-radius: 5px;
    display: none;
    z-index: 1001;
}

.toast.success {
    background: #28a745;
}

.toast.error {
    background: #dc3545;
}

.toast.warning {
    background: #ffc107;
    color: #333;
}

/* 订阅相关样式 */
.subscription-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    max-width: 600px;
    margin: 0 auto;
}

.subscription-card.premium {
    border: 3px solid #667eea;
}

.subscription-info {
    margin: 2rem 0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
}

.info-item label {
    font-weight: 600;
    color: #333;
}

.status.active {
    color: #28a745;
    font-weight: 600;
}

.status.inactive {
    color: #dc3545;
    font-weight: 600;
}

.renewal-reminder {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
    padding: 1rem;
    margin-top: 1.5rem;
    text-align: center;
}

.upgrade-section {
    margin-top: 2rem;
    text-align: center;
}

.upgrade-section h4 {
    margin-bottom: 1rem;
    color: #333;
}

.upgrade-section ul {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}

.upgrade-section li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

/* 订单支付样式 */
.order-info {
    margin: 1.5rem 0;
}

.amount {
    font-size: 1.2rem;
    font-weight: bold;
    color: #667eea;
}

.payment-notice {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 5px;
    padding: 1rem;
    margin: 1.5rem 0;
}

.payment-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* 表单样式增强 */
textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    font-family: monospace;
    resize: vertical;
    transition: border-color 0.3s;
}

textarea:focus {
    outline: none;
    border-color: #667eea;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #666;
    font-size: 1.1rem;
}

/* 按钮尺寸变体 */
.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
}

/* 功能列表样式 */
.features ul {
    list-style: none;
    text-align: left;
}

.features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.features li:before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
    margin-right: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .main-content {
        padding: 0 1rem;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .pricing-cards {
        grid-template-columns: 1fr;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .page-actions {
        flex-wrap: wrap;
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .payment-actions {
        flex-direction: column;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
