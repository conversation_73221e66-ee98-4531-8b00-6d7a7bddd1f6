<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steam账号管理工具 - 网络版</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div id="app">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <h1>Steam账号管理工具</h1>
                    <span class="version">网络版</span>
                </div>
                <div class="nav-menu" id="navMenu">
                    <div class="nav-item" id="loginBtn" onclick="showLogin()">登录</div>
                    <div class="nav-item" id="registerBtn" onclick="showRegister()">注册</div>
                    <div class="nav-item" id="userInfo" style="display: none;">
                        <span id="username"></span>
                        <div class="dropdown">
                            <div class="dropdown-item" onclick="showProfile()">个人信息</div>
                            <div class="dropdown-item" onclick="showSubscription()">订阅管理</div>
                            <div class="dropdown-item" onclick="logout()">退出登录</div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 欢迎页面 -->
            <div id="welcomePage" class="page active">
                <div class="welcome-container">
                    <h2>欢迎使用Steam账号管理工具网络版</h2>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>🔒 安全可靠</h3>
                            <p>数据加密存储，保护您的账号安全</p>
                        </div>
                        <div class="feature-card">
                            <h3>☁️ 云端同步</h3>
                            <p>多设备访问，数据实时同步</p>
                        </div>
                        <div class="feature-card">
                            <h3>🛡️ 封禁检测</h3>
                            <p>自动检测VAC和游戏封禁状态</p>
                        </div>
                        <div class="feature-card">
                            <h3>📊 统计分析</h3>
                            <p>账号状态统计，一目了然</p>
                        </div>
                    </div>
                    <div class="pricing-section">
                        <h3>定价方案</h3>
                        <div class="pricing-cards">
                            <div class="pricing-card">
                                <h4>免费版</h4>
                                <div class="price">¥0</div>
                                <ul>
                                    <li>本地数据存储</li>
                                    <li>基础账号管理</li>
                                    <li>封禁状态检测</li>
                                </ul>
                            </div>
                            <div class="pricing-card premium">
                                <h4>高级版</h4>
                                <div class="price">¥20<span>/年</span></div>
                                <ul>
                                    <li>云端数据存储</li>
                                    <li>多设备同步</li>
                                    <li>无限账号数量</li>
                                    <li>高级统计功能</li>
                                </ul>
                                <button class="btn btn-primary" onclick="showRegister()">立即开始</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 登录页面 -->
            <div id="loginPage" class="page">
                <div class="auth-container">
                    <h2>用户登录</h2>
                    <form id="loginForm" onsubmit="login(event)">
                        <div class="form-group">
                            <label for="loginUsername">用户名</label>
                            <input type="text" id="loginUsername" required>
                        </div>
                        <div class="form-group">
                            <label for="loginPassword">密码</label>
                            <input type="password" id="loginPassword" required>
                        </div>
                        <button type="submit" class="btn btn-primary">登录</button>
                    </form>
                    <p class="auth-switch">
                        还没有账号？<a href="#" onclick="showRegister()">立即注册</a>
                    </p>
                </div>
            </div>

            <!-- 注册页面 -->
            <div id="registerPage" class="page">
                <div class="auth-container">
                    <h2>用户注册</h2>
                    <form id="registerForm" onsubmit="register(event)">
                        <div class="form-group">
                            <label for="registerUsername">用户名</label>
                            <input type="text" id="registerUsername" required minlength="3" maxlength="20">
                        </div>
                        <div class="form-group">
                            <label for="registerEmail">邮箱</label>
                            <input type="email" id="registerEmail" required>
                        </div>
                        <div class="form-group">
                            <label for="registerPassword">密码</label>
                            <input type="password" id="registerPassword" required minlength="6">
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">确认密码</label>
                            <input type="password" id="confirmPassword" required>
                        </div>
                        <button type="submit" class="btn btn-primary">注册</button>
                    </form>
                    <p class="auth-switch">
                        已有账号？<a href="#" onclick="showLogin()">立即登录</a>
                    </p>
                </div>
            </div>

            <!-- 账号管理页面 -->
            <div id="accountsPage" class="page">
                <div class="page-header">
                    <h2>账号管理</h2>
                    <div class="page-actions">
                        <button class="btn btn-secondary" onclick="showAddAccount()">添加账号</button>
                        <button class="btn btn-secondary" onclick="showImportAccounts()">批量导入</button>
                        <button class="btn btn-secondary" onclick="batchCheckSteamID()">批量获取SteamID</button>
                        <button class="btn btn-secondary" onclick="batchCheckBan()">批量检查封禁</button>
                    </div>
                </div>
                
                <!-- 账号统计 -->
                <div class="stats-container" id="accountStats">
                    <!-- 统计信息将通过JavaScript动态加载 -->
                </div>
                
                <!-- 账号列表 -->
                <div class="accounts-container">
                    <div class="accounts-table" id="accountsTable">
                        <!-- 账号列表将通过JavaScript动态加载 -->
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination" id="pagination">
                        <!-- 分页将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 订阅管理页面 -->
            <div id="subscriptionPage" class="page">
                <div class="page-header">
                    <h2>订阅管理</h2>
                </div>
                <div class="subscription-container" id="subscriptionContainer">
                    <!-- 订阅信息将通过JavaScript动态加载 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modalBody">
                <!-- 模态框内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast">
        <span id="toastMessage"></span>
    </div>

    <script src="/static/js/app.js"></script>
</body>
</html>
