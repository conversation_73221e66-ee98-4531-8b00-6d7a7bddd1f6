// 全局变量
let currentUser = null;
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;

// API基础URL
const API_BASE = '/api/v1';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已登录
    const token = localStorage.getItem('token');
    if (token) {
        // 验证token并获取用户信息
        fetchProfile();
    } else {
        showWelcome();
    }
});

// 显示页面
function showPage(pageId) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    
    // 显示指定页面
    document.getElementById(pageId).classList.add('active');
}

// 显示欢迎页面
function showWelcome() {
    showPage('welcomePage');
    updateNavigation(false);
}

// 显示登录页面
function showLogin() {
    showPage('loginPage');
    document.getElementById('loginForm').reset();
}

// 显示注册页面
function showRegister() {
    showPage('registerPage');
    document.getElementById('registerForm').reset();
}

// 显示账号管理页面
function showAccounts() {
    if (!currentUser) {
        showLogin();
        return;
    }
    showPage('accountsPage');
    loadAccountStats();
    loadAccounts();
}

// 显示订阅管理页面
function showSubscription() {
    if (!currentUser) {
        showLogin();
        return;
    }
    showPage('subscriptionPage');
    loadSubscription();
}

// 显示个人信息页面
function showProfile() {
    if (!currentUser) {
        showLogin();
        return;
    }
    // TODO: 实现个人信息页面
    showToast('个人信息功能开发中...', 'warning');
}

// 更新导航栏
function updateNavigation(isLoggedIn) {
    const loginBtn = document.getElementById('loginBtn');
    const registerBtn = document.getElementById('registerBtn');
    const userInfo = document.getElementById('userInfo');
    const username = document.getElementById('username');
    
    if (isLoggedIn && currentUser) {
        loginBtn.style.display = 'none';
        registerBtn.style.display = 'none';
        userInfo.style.display = 'block';
        username.textContent = currentUser.username;
    } else {
        loginBtn.style.display = 'block';
        registerBtn.style.display = 'block';
        userInfo.style.display = 'none';
    }
}

// 用户注册
async function register(event) {
    event.preventDefault();
    
    const username = document.getElementById('registerUsername').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (password !== confirmPassword) {
        showToast('两次输入的密码不一致', 'error');
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username,
                email,
                password
            })
        });
        
        const data = await response.json();
        
        if (data.code === 0) {
            localStorage.setItem('token', data.data.token);
            currentUser = data.data.user;
            updateNavigation(true);
            showAccounts();
            showToast('注册成功！', 'success');
        } else {
            showToast(data.message, 'error');
        }
    } catch (error) {
        showToast('注册失败：' + error.message, 'error');
    }
}

// 用户登录
async function login(event) {
    event.preventDefault();
    
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;
    
    try {
        const response = await fetch(`${API_BASE}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username,
                password
            })
        });
        
        const data = await response.json();
        
        if (data.code === 0) {
            localStorage.setItem('token', data.data.token);
            currentUser = data.data.user;
            updateNavigation(true);
            showAccounts();
            showToast('登录成功！', 'success');
        } else {
            showToast(data.message, 'error');
        }
    } catch (error) {
        showToast('登录失败：' + error.message, 'error');
    }
}

// 获取用户信息
async function fetchProfile() {
    try {
        const response = await fetch(`${API_BASE}/profile`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        const data = await response.json();
        
        if (data.code === 0) {
            currentUser = data.data;
            updateNavigation(true);
            showAccounts();
        } else {
            localStorage.removeItem('token');
            showWelcome();
        }
    } catch (error) {
        localStorage.removeItem('token');
        showWelcome();
    }
}

// 用户退出
function logout() {
    localStorage.removeItem('token');
    currentUser = null;
    updateNavigation(false);
    showWelcome();
    showToast('已退出登录', 'success');
}

// 加载账号统计
async function loadAccountStats() {
    try {
        const response = await fetch(`${API_BASE}/accounts/stats`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        const data = await response.json();
        
        if (data.code === 0) {
            displayAccountStats(data.data);
        }
    } catch (error) {
        console.error('Failed to load account stats:', error);
    }
}

// 显示账号统计
function displayAccountStats(stats) {
    const container = document.getElementById('accountStats');
    container.innerHTML = `
        <div class="stat-card">
            <div class="stat-number">${stats.total}</div>
            <div class="stat-label">总账号数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.normal}</div>
            <div class="stat-label">正常账号</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.vac_ban}</div>
            <div class="stat-label">VAC封禁</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.game_ban}</div>
            <div class="stat-label">游戏封禁</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.unknown}</div>
            <div class="stat-label">未知状态</div>
        </div>
    `;
}

// 加载账号列表
async function loadAccounts(page = 1) {
    try {
        const response = await fetch(`${API_BASE}/accounts?page=${page}&page_size=${pageSize}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        const data = await response.json();
        
        if (data.code === 0) {
            currentPage = data.data.page;
            totalPages = data.data.total_pages;
            displayAccounts(data.data.accounts);
            displayPagination();
        }
    } catch (error) {
        console.error('Failed to load accounts:', error);
    }
}

// 显示账号列表
function displayAccounts(accounts) {
    const container = document.getElementById('accountsTable');
    
    if (accounts.length === 0) {
        container.innerHTML = '<div class="empty-state">暂无账号数据</div>';
        return;
    }
    
    let html = `
        <table class="table">
            <thead>
                <tr>
                    <th>用户名</th>
                    <th>SteamID</th>
                    <th>状态</th>
                    <th>最后登录</th>
                    <th>备注</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    accounts.forEach(account => {
        const statusClass = getStatusClass(account.status);
        const lastLogin = account.last_login ? new Date(account.last_login).toLocaleString() : '从未登录';
        
        html += `
            <tr>
                <td>${account.username}</td>
                <td>${account.steam_id || '未获取'}</td>
                <td><span class="status-badge ${statusClass}">${account.status}</span></td>
                <td>${lastLogin}</td>
                <td>${account.notes || '-'}</td>
                <td>
                    <button class="btn btn-secondary btn-sm" onclick="editAccount(${account.id})">编辑</button>
                    <button class="btn btn-danger btn-sm" onclick="deleteAccount(${account.id})">删除</button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    container.innerHTML = html;
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case '正常':
            return 'status-normal';
        case 'VAC封禁':
        case '游戏封禁':
            return 'status-banned';
        default:
            return 'status-unknown';
    }
}

// 显示分页
function displayPagination() {
    const container = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        container.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // 上一页
    html += `<button ${currentPage === 1 ? 'disabled' : ''} onclick="loadAccounts(${currentPage - 1})">上一页</button>`;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage) {
            html += `<button class="active">${i}</button>`;
        } else {
            html += `<button onclick="loadAccounts(${i})">${i}</button>`;
        }
    }
    
    // 下一页
    html += `<button ${currentPage === totalPages ? 'disabled' : ''} onclick="loadAccounts(${currentPage + 1})">下一页</button>`;
    
    container.innerHTML = html;
}

// 显示添加账号模态框
function showAddAccount() {
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h3>添加账号</h3>
        <form id="addAccountForm" onsubmit="addAccount(event)">
            <div class="form-group">
                <label for="accountUsername">用户名</label>
                <input type="text" id="accountUsername" required>
            </div>
            <div class="form-group">
                <label for="accountPassword">密码</label>
                <input type="password" id="accountPassword" required>
            </div>
            <div class="form-group">
                <label for="accountNotes">备注</label>
                <input type="text" id="accountNotes">
            </div>
            <button type="submit" class="btn btn-primary">添加</button>
            <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
        </form>
    `;
    showModal();
}

// 添加账号
async function addAccount(event) {
    event.preventDefault();
    
    const username = document.getElementById('accountUsername').value;
    const password = document.getElementById('accountPassword').value;
    const notes = document.getElementById('accountNotes').value;
    
    try {
        const response = await fetch(`${API_BASE}/accounts`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                username,
                password,
                notes
            })
        });
        
        const data = await response.json();
        
        if (data.code === 0) {
            closeModal();
            loadAccounts(currentPage);
            loadAccountStats();
            showToast('账号添加成功！', 'success');
        } else {
            showToast(data.message, 'error');
        }
    } catch (error) {
        showToast('添加失败：' + error.message, 'error');
    }
}

// 批量检查SteamID
async function batchCheckSteamID() {
    try {
        const response = await fetch(`${API_BASE}/steam/batch-check-steamid`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        const data = await response.json();
        
        if (data.code === 0) {
            showToast('SteamID检查任务已启动，请稍后刷新查看结果', 'success');
        } else {
            showToast(data.message, 'error');
        }
    } catch (error) {
        showToast('操作失败：' + error.message, 'error');
    }
}

// 批量检查封禁状态
async function batchCheckBan() {
    try {
        const response = await fetch(`${API_BASE}/steam/batch-check-ban`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        const data = await response.json();

        if (data.code === 0) {
            showToast('封禁检查任务已启动，请稍后刷新查看结果', 'success');
        } else {
            showToast(data.message, 'error');
        }
    } catch (error) {
        showToast('操作失败：' + error.message, 'error');
    }
}

// 编辑账号
function editAccount(accountId) {
    // TODO: 实现编辑账号功能
    showToast('编辑功能开发中...', 'warning');
}

// 删除账号
async function deleteAccount(accountId) {
    if (!confirm('确定要删除这个账号吗？')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/accounts/${accountId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        const data = await response.json();

        if (data.code === 0) {
            loadAccounts(currentPage);
            loadAccountStats();
            showToast('账号删除成功！', 'success');
        } else {
            showToast(data.message, 'error');
        }
    } catch (error) {
        showToast('删除失败：' + error.message, 'error');
    }
}

// 显示批量导入模态框
function showImportAccounts() {
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h3>批量导入账号</h3>
        <p>请按照以下格式输入账号信息，每行一个账号：</p>
        <p><code>用户名:密码:备注</code></p>
        <form id="importAccountsForm" onsubmit="importAccounts(event)">
            <div class="form-group">
                <label for="accountsText">账号列表</label>
                <textarea id="accountsText" rows="10" placeholder="例如：&#10;username1:password1:备注1&#10;username2:password2:备注2" required></textarea>
            </div>
            <button type="submit" class="btn btn-primary">导入</button>
            <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
        </form>
    `;
    showModal();
}

// 批量导入账号
async function importAccounts(event) {
    event.preventDefault();

    const accountsText = document.getElementById('accountsText').value;
    const lines = accountsText.split('\n').filter(line => line.trim() && !line.startsWith('#'));

    if (lines.length === 0) {
        showToast('请输入有效的账号信息', 'error');
        return;
    }

    const accounts = [];
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        const parts = line.split(':');

        if (parts.length < 2) {
            showToast(`第${i + 1}行格式错误，应为：用户名:密码:备注`, 'error');
            return;
        }

        accounts.push({
            username: parts[0],
            password: parts[1],
            notes: parts[2] || ''
        });
    }

    try {
        const response = await fetch(`${API_BASE}/accounts/import`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                accounts: accounts
            })
        });

        const data = await response.json();

        if (data.code === 0) {
            closeModal();
            loadAccounts(currentPage);
            loadAccountStats();
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
    } catch (error) {
        showToast('导入失败：' + error.message, 'error');
    }
}

// 加载订阅信息
async function loadSubscription() {
    try {
        const response = await fetch(`${API_BASE}/subscription`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        const data = await response.json();

        if (data.code === 0) {
            displaySubscription(data.data);
        }
    } catch (error) {
        console.error('Failed to load subscription:', error);
    }
}

// 显示订阅信息
function displaySubscription(subscription) {
    const container = document.getElementById('subscriptionContainer');

    if (!subscription) {
        container.innerHTML = `
            <div class="subscription-card">
                <h3>免费版用户</h3>
                <p>您当前使用的是免费版本，功能有限。</p>
                <div class="features">
                    <h4>免费版功能：</h4>
                    <ul>
                        <li>本地数据存储</li>
                        <li>基础账号管理</li>
                        <li>封禁状态检测</li>
                    </ul>
                </div>
                <div class="upgrade-section">
                    <h4>升级到高级版：</h4>
                    <ul>
                        <li>云端数据存储</li>
                        <li>多设备同步</li>
                        <li>无限账号数量</li>
                        <li>高级统计功能</li>
                    </ul>
                    <button class="btn btn-primary" onclick="createOrder()">立即升级 - ¥20/年</button>
                </div>
            </div>
        `;
    } else {
        const endDate = new Date(subscription.end_time).toLocaleDateString();
        container.innerHTML = `
            <div class="subscription-card premium">
                <h3>高级版用户</h3>
                <p>感谢您支持我们的产品！</p>
                <div class="subscription-info">
                    <div class="info-item">
                        <label>订阅状态：</label>
                        <span class="status ${subscription.is_active ? 'active' : 'inactive'}">
                            ${subscription.is_active ? '已激活' : '已过期'}
                        </span>
                    </div>
                    <div class="info-item">
                        <label>到期时间：</label>
                        <span>${endDate}</span>
                    </div>
                    <div class="info-item">
                        <label>剩余天数：</label>
                        <span>${subscription.days_left}天</span>
                    </div>
                </div>
                ${subscription.days_left < 30 ? `
                    <div class="renewal-reminder">
                        <p>⚠️ 您的订阅即将到期，请及时续费以继续享受高级功能。</p>
                        <button class="btn btn-primary" onclick="createOrder()">立即续费 - ¥20/年</button>
                    </div>
                ` : ''}
            </div>
        `;
    }
}

// 创建订单
async function createOrder() {
    try {
        const response = await fetch(`${API_BASE}/orders`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                pay_method: 'alipay'
            })
        });

        const data = await response.json();

        if (data.code === 0) {
            showPaymentModal(data.data);
        } else {
            showToast(data.message, 'error');
        }
    } catch (error) {
        showToast('创建订单失败：' + error.message, 'error');
    }
}

// 显示支付模态框
function showPaymentModal(order) {
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h3>订单支付</h3>
        <div class="order-info">
            <div class="info-item">
                <label>订单号：</label>
                <span>${order.order_no}</span>
            </div>
            <div class="info-item">
                <label>商品：</label>
                <span>${order.description}</span>
            </div>
            <div class="info-item">
                <label>金额：</label>
                <span class="amount">¥${order.amount}</span>
            </div>
            <div class="info-item">
                <label>支付方式：</label>
                <span>${order.pay_method}</span>
            </div>
        </div>
        <div class="payment-notice">
            <p>⚠️ 这是演示版本，点击"确认支付"将模拟支付成功。</p>
            <p>在实际项目中，这里应该集成真实的支付接口。</p>
        </div>
        <div class="payment-actions">
            <button class="btn btn-primary" onclick="payOrder(${order.id})">确认支付</button>
            <button class="btn btn-secondary" onclick="closeModal()">取消</button>
        </div>
    `;
    showModal();
}

// 支付订单
async function payOrder(orderId) {
    try {
        const response = await fetch(`${API_BASE}/orders/${orderId}/pay`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        const data = await response.json();

        if (data.code === 0) {
            closeModal();
            showToast('支付成功！会员已激活', 'success');
            // 刷新用户信息
            fetchProfile();
            // 如果在订阅页面，刷新订阅信息
            if (document.getElementById('subscriptionPage').classList.contains('active')) {
                loadSubscription();
            }
        } else {
            showToast(data.message, 'error');
        }
    } catch (error) {
        showToast('支付失败：' + error.message, 'error');
    }
}

// 显示模态框
function showModal() {
    document.getElementById('modal').style.display = 'block';
}

// 关闭模态框
function closeModal() {
    document.getElementById('modal').style.display = 'none';
}

// 显示消息提示
function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    const toastMessage = document.getElementById('toastMessage');
    
    toastMessage.textContent = message;
    toast.className = `toast ${type}`;
    toast.style.display = 'block';
    
    setTimeout(() => {
        toast.style.display = 'none';
    }, 3000);
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('modal');
    if (event.target === modal) {
        closeModal();
    }
}
