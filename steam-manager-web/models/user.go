package models

import (
	"time"
)

// User 用户模型
type User struct {
	ID          int       `json:"id" db:"id"`
	Username    string    `json:"username" db:"username"`
	Email       string    `json:"email" db:"email"`
	Password    string    `json:"-" db:"password"` // 不返回密码
	IsPremium   bool      `json:"is_premium" db:"is_premium"`
	ExpireTime  *time.Time `json:"expire_time" db:"expire_time"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// UserRegisterRequest 用户注册请求
type UserRegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=20"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID         int        `json:"id"`
	Username   string     `json:"username"`
	Email      string     `json:"email"`
	IsPremium  bool       `json:"is_premium"`
	ExpireTime *time.Time `json:"expire_time"`
	CreatedAt  time.Time  `json:"created_at"`
}

// ToResponse 转换为响应格式
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:         u.ID,
		Username:   u.Username,
		Email:      u.Email,
		IsPremium:  u.IsPremium,
		ExpireTime: u.ExpireTime,
		CreatedAt:  u.CreatedAt,
	}
}

// IsExpired 检查是否过期
func (u *User) IsExpired() bool {
	if !u.IsPremium || u.ExpireTime == nil {
		return false
	}
	return time.Now().After(*u.ExpireTime)
}

// CanUseCloudStorage 是否可以使用云端存储
func (u *User) CanUseCloudStorage() bool {
	return u.IsPremium && !u.IsExpired()
}
