package models

import (
	"time"
)

// Order 订单模型
type Order struct {
	ID          int       `json:"id" db:"id"`
	UserID      int       `json:"user_id" db:"user_id"`
	OrderNo     string    `json:"order_no" db:"order_no"`
	Amount      float64   `json:"amount" db:"amount"`
	Status      int       `json:"status" db:"status"` // 0:待支付, 1:已支付, 2:已取消, 3:已退款
	PayMethod   string    `json:"pay_method" db:"pay_method"`
	PayTime     *time.Time `json:"pay_time" db:"pay_time"`
	ExpireTime  time.Time `json:"expire_time" db:"expire_time"`
	Description string    `json:"description" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	PayMethod string `json:"pay_method" binding:"required"` // alipay, wechat, etc.
}

// OrderResponse 订单响应
type OrderResponse struct {
	ID          int        `json:"id"`
	OrderNo     string     `json:"order_no"`
	Amount      float64    `json:"amount"`
	Status      string     `json:"status"`
	PayMethod   string     `json:"pay_method"`
	PayTime     *time.Time `json:"pay_time"`
	ExpireTime  time.Time  `json:"expire_time"`
	Description string     `json:"description"`
	CreatedAt   time.Time  `json:"created_at"`
}

// ToResponse 转换为响应格式
func (o *Order) ToResponse() *OrderResponse {
	status := "未知"
	switch o.Status {
	case 0:
		status = "待支付"
	case 1:
		status = "已支付"
	case 2:
		status = "已取消"
	case 3:
		status = "已退款"
	}
	
	return &OrderResponse{
		ID:          o.ID,
		OrderNo:     o.OrderNo,
		Amount:      o.Amount,
		Status:      status,
		PayMethod:   o.PayMethod,
		PayTime:     o.PayTime,
		ExpireTime:  o.ExpireTime,
		Description: o.Description,
		CreatedAt:   o.CreatedAt,
	}
}

// Subscription 订阅模型
type Subscription struct {
	ID         int       `json:"id" db:"id"`
	UserID     int       `json:"user_id" db:"user_id"`
	StartTime  time.Time `json:"start_time" db:"start_time"`
	EndTime    time.Time `json:"end_time" db:"end_time"`
	IsActive   bool      `json:"is_active" db:"is_active"`
	OrderID    int       `json:"order_id" db:"order_id"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`
}

// SubscriptionResponse 订阅响应
type SubscriptionResponse struct {
	ID        int       `json:"id"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	IsActive  bool      `json:"is_active"`
	DaysLeft  int       `json:"days_left"`
	CreatedAt time.Time `json:"created_at"`
}

// ToResponse 转换为响应格式
func (s *Subscription) ToResponse() *SubscriptionResponse {
	daysLeft := 0
	if s.IsActive && time.Now().Before(s.EndTime) {
		daysLeft = int(s.EndTime.Sub(time.Now()).Hours() / 24)
	}
	
	return &SubscriptionResponse{
		ID:        s.ID,
		StartTime: s.StartTime,
		EndTime:   s.EndTime,
		IsActive:  s.IsActive,
		DaysLeft:  daysLeft,
		CreatedAt: s.CreatedAt,
	}
}
