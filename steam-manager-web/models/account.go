package models

import (
	"time"
)

// SteamAccount Steam账号模型
type SteamAccount struct {
	ID            int        `json:"id" db:"id"`
	UserID        int        `json:"user_id" db:"user_id"`
	Username      string     `json:"username" db:"username"`
	Password      string     `json:"password" db:"password"`
	SteamID       string     `json:"steam_id" db:"steam_id"`
	PUBGBanStatus int        `json:"pubg_ban_status" db:"pubg_ban_status"` // -1:未知, 0:正常, 1:VAC封禁, 2:游戏封禁
	Status        string     `json:"status"`                               // 状态文本：正常、VAC封禁、游戏封禁、未知
	LastLogin     *time.Time `json:"last_login" db:"last_login"`
	LastChecked   *time.Time `json:"last_checked" db:"last_checked"`
	Notes         string     `json:"notes" db:"notes"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`
}

// SteamAccountRequest Steam账号请求
type SteamAccountRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Notes    string `json:"notes"`
}

// SteamAccountResponse Steam账号响应
type SteamAccountResponse struct {
	ID            int        `json:"id"`
	Username      string     `json:"username"`
	SteamID       string     `json:"steam_id"`
	Status        string     `json:"status"`
	LastLogin     *time.Time `json:"last_login"`
	LastChecked   *time.Time `json:"last_checked"`
	Notes         string     `json:"notes"`
	CreatedAt     time.Time  `json:"created_at"`
	IsLoggedIn    bool       `json:"is_logged_in"`
}

// ToResponse 转换为响应格式
func (sa *SteamAccount) ToResponse() *SteamAccountResponse {
	status := "未知"
	switch sa.PUBGBanStatus {
	case 0:
		status = "正常"
	case 1:
		status = "VAC封禁"
	case 2:
		status = "游戏封禁"
	}
	
	return &SteamAccountResponse{
		ID:          sa.ID,
		Username:    sa.Username,
		SteamID:     sa.SteamID,
		Status:      status,
		LastLogin:   sa.LastLogin,
		LastChecked: sa.LastChecked,
		Notes:       sa.Notes,
		CreatedAt:   sa.CreatedAt,
		IsLoggedIn:  false, // 这个需要实时检测
	}
}

// ImportAccountsRequest 批量导入账号请求
type ImportAccountsRequest struct {
	Accounts []SteamAccountRequest `json:"accounts" binding:"required"`
}

// AccountStats 账号统计
type AccountStats struct {
	Total    int `json:"total"`
	Normal   int `json:"normal"`
	VACBan   int `json:"vac_ban"`
	GameBan  int `json:"game_ban"`
	Unknown  int `json:"unknown"`
}

// PaginatedAccountsResponse 分页账号响应
type PaginatedAccountsResponse struct {
	Accounts   []*SteamAccountResponse `json:"accounts"`
	Total      int                     `json:"total"`
	Page       int                     `json:"page"`
	PageSize   int                     `json:"page_size"`
	TotalPages int                     `json:"total_pages"`
}
