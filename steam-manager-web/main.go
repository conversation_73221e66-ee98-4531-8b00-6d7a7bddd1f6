package main

import (
	"log"
	"os"

	"steam-manager-web/config"
	"steam-manager-web/database"
	"steam-manager-web/services"
	"steam-manager-web/ui"
)

func main() {
	// 加载配置
	if err := config.LoadConfig("config.json"); err != nil {
		log.Printf("Warning: Failed to load config: %v", err)
		log.Println("Using default configuration...")
	}

	// 初始化数据库（可选，支持本地SQLite作为备用）
	var useDatabase bool
	if err := database.InitDB(); err != nil {
		log.Printf("Warning: Failed to initialize database: %v", err)
		log.Println("Running in local mode without cloud sync...")
		useDatabase = false
	} else {
		defer database.CloseDB()

		// 创建数据表
		if err := database.CreateTables(); err != nil {
			log.Printf("Warning: Failed to create tables: %v", err)
			useDatabase = false
		} else {
			useDatabase = true
			log.Println("Database connected successfully - Cloud sync enabled")
		}
	}

	// 初始化服务
	accountService := services.NewAccountService(useDatabase)
	steamService := services.NewSteamService()
	authService := services.NewAuthService(useDatabase)

	// 检查是否已登录（网络版需要用户认证）
	if useDatabase {
		if !authService.IsLoggedIn() {
			// 显示登录界面
			loginUI := ui.NewLoginUI(authService)
			if !loginUI.Run() {
				log.Println("Login cancelled or failed")
				os.Exit(0)
			}
		}
	}

	// 创建并运行终端UI
	terminalUI := ui.NewTerminalUI(accountService, steamService, authService, config.AppConfig)
	terminalUI.Run()
}
