# Steam账号管理工具网络版 - 部署指南

## 🚀 快速部署

### 1. 环境准备

#### 系统要求
- **操作系统**: Linux/Windows/macOS
- **Go版本**: 1.23+
- **数据库**: MySQL 5.7+ 或 MariaDB 10.3+
- **内存**: 建议2GB+
- **存储**: 建议10GB+

#### 安装依赖
```bash
# 安装Go（如果未安装）
# 下载地址：https://golang.org/dl/

# 安装MySQL（如果未安装）
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# 或使用Docker
docker run -d --name mysql \
  -e MYSQL_ROOT_PASSWORD=your_password \
  -e MYSQL_DATABASE=steam_manager \
  -p 3306:3306 \
  mysql:8.0
```

### 2. 数据库配置

#### 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE steam_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'steam_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON steam_manager.* TO 'steam_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 初始化表结构
```bash
# 使用提供的SQL脚本
mysql -u root -p steam_manager < database/init.sql
```

### 3. 应用部署

#### 方式一：源码部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd steam-manager-web

# 2. 安装依赖
go mod tidy

# 3. 配置文件
cp config.example.json config.json
# 编辑config.json，修改数据库连接信息

# 4. 构建应用
go build -o steam-manager-web main.go

# 5. 运行应用
./steam-manager-web
```

#### 方式二：Docker部署
```bash
# 1. 创建Dockerfile
cat > Dockerfile << EOF
FROM golang:1.23-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN go build -o steam-manager-web main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/steam-manager-web .
COPY --from=builder /app/static ./static
COPY --from=builder /app/config.example.json ./config.json
EXPOSE 8080
CMD ["./steam-manager-web"]
EOF

# 2. 构建镜像
docker build -t steam-manager-web .

# 3. 运行容器
docker run -d --name steam-manager \
  -p 8080:8080 \
  -v $(pwd)/config.json:/root/config.json \
  steam-manager-web
```

#### 方式三：Docker Compose部署
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: steam-mysql
    environment:
      MYSQL_ROOT_PASSWORD: your_root_password
      MYSQL_DATABASE: steam_manager
      MYSQL_USER: steam_user
      MYSQL_PASSWORD: your_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  app:
    build: .
    container_name: steam-app
    ports:
      - "8080:8080"
    volumes:
      - ./config.json:/root/config.json
    depends_on:
      - mysql
    restart: unless-stopped

volumes:
  mysql_data:
```

```bash
# 启动服务
docker-compose up -d
```

### 4. 反向代理配置

#### Nginx配置
```nginx
# /etc/nginx/sites-available/steam-manager
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件缓存
    location /static/ {
        proxy_pass http://localhost:8080;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# HTTPS配置（使用Let's Encrypt）
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### Apache配置
```apache
# /etc/apache2/sites-available/steam-manager.conf
<VirtualHost *:80>
    ServerName your-domain.com
    
    ProxyPreserveHost On
    ProxyRequests Off
    ProxyPass / http://localhost:8080/
    ProxyPassReverse / http://localhost:8080/
    
    # 静态文件缓存
    <LocationMatch "^/static/">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </LocationMatch>
</VirtualHost>
```

### 5. 系统服务配置

#### Systemd服务
```ini
# /etc/systemd/system/steam-manager.service
[Unit]
Description=Steam Manager Web Service
After=network.target mysql.service

[Service]
Type=simple
User=steam
Group=steam
WorkingDirectory=/opt/steam-manager
ExecStart=/opt/steam-manager/steam-manager-web
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```bash
# 启用服务
sudo systemctl enable steam-manager
sudo systemctl start steam-manager
sudo systemctl status steam-manager
```

### 6. 安全配置

#### 防火墙设置
```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# iptables
iptables -A INPUT -p tcp --dport 22 -j ACCEPT
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

#### SSL证书配置
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 7. 监控和日志

#### 日志配置
```bash
# 创建日志目录
sudo mkdir -p /var/log/steam-manager
sudo chown steam:steam /var/log/steam-manager

# 配置logrotate
cat > /etc/logrotate.d/steam-manager << EOF
/var/log/steam-manager/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 steam steam
}
EOF
```

#### 监控脚本
```bash
#!/bin/bash
# /opt/steam-manager/monitor.sh

# 检查服务状态
if ! systemctl is-active --quiet steam-manager; then
    echo "Steam Manager service is down, restarting..."
    systemctl restart steam-manager
fi

# 检查数据库连接
if ! mysqladmin ping -h localhost --silent; then
    echo "MySQL is down!"
    # 发送告警邮件或通知
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is above 80%: ${DISK_USAGE}%"
fi
```

### 8. 备份策略

#### 数据库备份
```bash
#!/bin/bash
# /opt/steam-manager/backup.sh

BACKUP_DIR="/opt/backups/steam-manager"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u root -p steam_manager > $BACKUP_DIR/steam_manager_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/steam_manager_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Backup completed: steam_manager_$DATE.sql.gz"
```

#### 定时备份
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /opt/steam-manager/backup.sh
```

### 9. 性能优化

#### 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_accounts_user_status ON steam_accounts(user_id, pubg_ban_status);
CREATE INDEX idx_orders_user_status ON orders(user_id, status);

-- 优化配置
SET GLOBAL innodb_buffer_pool_size = 1G;
SET GLOBAL max_connections = 200;
```

#### 应用优化
```json
{
  "server": {
    "mode": "release"
  },
  "database": {
    "max_open_conns": 100,
    "max_idle_conns": 10
  }
}
```

### 10. 故障排除

#### 常见问题
1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接配置
   - 检查防火墙设置

2. **应用启动失败**
   - 检查配置文件格式
   - 验证端口是否被占用
   - 查看应用日志

3. **静态文件无法访问**
   - 检查文件权限
   - 验证路径配置
   - 检查Nginx/Apache配置

#### 日志查看
```bash
# 应用日志
journalctl -u steam-manager -f

# Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# MySQL日志
tail -f /var/log/mysql/error.log
```

## 📞 技术支持

如果在部署过程中遇到问题，请：
1. 查看相关日志文件
2. 检查配置文件格式
3. 验证网络连接
4. 提交Issue描述问题
