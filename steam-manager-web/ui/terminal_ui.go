package ui

import (
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/fatih/color"
	"steam-manager-web/config"
	"steam-manager-web/models"
	"steam-manager-web/services"
)

// TerminalUI 终端用户界面
type TerminalUI struct {
	accountService *services.AccountService
	steamService   *services.SteamService
	authService    *services.AuthService
	config         *config.Config
	terminalWidth  int
	terminalHeight int
	currentUser    *models.User
}

// NewTerminalUI 创建终端界面
func NewTerminalUI(accountService *services.AccountService, steamService *services.SteamService, authService *services.AuthService, cfg *config.Config) *TerminalUI {
	ui := &TerminalUI{
		accountService: accountService,
		steamService:   steamService,
		authService:    authService,
		config:         cfg,
	}
	
	ui.updateTerminalSize()
	
	// 获取当前用户信息
	if authService != nil {
		ui.currentUser = authService.GetCurrentUser()
	}
	
	return ui
}

// updateTerminalSize 更新终端尺寸
func (ui *TerminalUI) updateTerminalSize() {
	size := ui.getTerminalSize()
	ui.terminalWidth = size.Width
	ui.terminalHeight = size.Height
}

// getTerminalSize 获取终端尺寸
func (ui *TerminalUI) getTerminalSize() TerminalSize {
	defaultSize := TerminalSize{Width: 80, Height: 25}
	
	if runtime.GOOS == "windows" {
		cmd := exec.Command("powershell", "-Command", "(Get-Host).UI.RawUI.WindowSize.Height, (Get-Host).UI.RawUI.WindowSize.Width")
		output, err := cmd.Output()
		if err == nil {
			parts := strings.Fields(strings.TrimSpace(string(output)))
			if len(parts) == 2 {
				if height, err := strconv.Atoi(parts[0]); err == nil {
					defaultSize.Height = height
				}
				if width, err := strconv.Atoi(parts[1]); err == nil {
					defaultSize.Width = width
				}
			}
		}
	}
	
	return defaultSize
}

// TerminalSize 终端尺寸
type TerminalSize struct {
	Width  int
	Height int
}

// clearScreen 清屏
func (ui *TerminalUI) clearScreen() {
	if runtime.GOOS == "windows" {
		cmd := exec.Command("cmd", "/c", "cls")
		cmd.Stdout = os.Stdout
		cmd.Run()
	} else {
		cmd := exec.Command("clear")
		cmd.Stdout = os.Stdout
		cmd.Run()
	}
}

// Run 运行主界面
func (ui *TerminalUI) Run() {
	ui.showWelcome()
	
	for {
		ui.showMainAccountList()
	}
}

// showWelcome 显示欢迎信息
func (ui *TerminalUI) showWelcome() {
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                        Steam账号管理工具 - 网络版                           ║")
	color.Cyan("║                              版本: v1.0                                     ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	
	// 显示用户信息和版本信息
	if ui.currentUser != nil {
		if ui.currentUser.IsPremium {
			color.Green("🌟 当前用户: %s (高级版用户)", ui.currentUser.Username)
			if ui.currentUser.ExpireTime != nil {
				color.White("   会员到期: %s", ui.currentUser.ExpireTime.Format("2006-01-02"))
			}
		} else {
			color.Yellow("👤 当前用户: %s (免费版用户)", ui.currentUser.Username)
		}
		color.Cyan("☁️  云端同步: 已启用")
	} else {
		color.White("💻 本地模式: 数据存储在本地")
	}
	
	fmt.Println()
	color.White("正在初始化...")
	time.Sleep(1 * time.Second)
}

// showMainAccountList 显示主账号列表
func (ui *TerminalUI) showMainAccountList() {
	for {
		accounts, err := ui.accountService.GetAllAccounts()
		if err != nil {
			color.Red("获取账号失败: %v", err)
			ui.waitForEnter()
			return
		}
		
		if len(accounts) == 0 {
			ui.showEmptyAccountList()
			continue
		}
		
		// 显示交互式账号列表
		shouldContinue := ui.showInteractiveAccountList(accounts)
		if !shouldContinue {
			return
		}
	}
}

// showEmptyAccountList 显示空账号列表
func (ui *TerminalUI) showEmptyAccountList() {
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                            Steam 账号列表                                   ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	color.Yellow("📝 暂无账号")
	fmt.Println()
	color.Cyan("操作说明:")
	color.White("  A 添加账号  M 更多功能  ESC/Q 退出程序")
	
	action := ui.getKeyInput()
	switch action {
	case "add":
		ui.addAccount()
	case "menu":
		ui.showMoreMenu()
	case "escape", "q":
		ui.showGoodbye()
		os.Exit(0)
	}
}

// showInteractiveAccountList 显示交互式账号列表
func (ui *TerminalUI) showInteractiveAccountList(accounts []*models.SteamAccount) bool {
	ui.clearScreen()
	
	// 显示标题
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                        Steam 账号管理工具 - 网络版                          ║")
	color.Cyan("║                            Steam 账号列表                                   ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Printf("\n总计: %d 个账号", len(accounts))
	if ui.currentUser != nil {
		if ui.currentUser.IsPremium {
			color.Green(" | 高级版用户 ✨")
		} else {
			color.Yellow(" | 免费版用户")
		}
	}
	fmt.Println()
	
	// 显示账号列表
	ui.displayAccountTable(accounts)
	
	fmt.Println()
	color.Cyan("操作说明:")
	color.White("  A 添加账号  D 删除账号  L 登录Steam  M 更多功能  ESC/Q 退出")
	
	action := ui.getKeyInput()
	switch action {
	case "add":
		ui.addAccount()
		return true
	case "delete":
		ui.deleteAccount(accounts)
		return true
	case "login":
		ui.loginSteam(accounts)
		return true
	case "menu":
		ui.showMoreMenu()
		return true
	case "escape", "q":
		ui.showGoodbye()
		return false
	default:
		return true
	}
}

// displayAccountTable 显示账号表格
func (ui *TerminalUI) displayAccountTable(accounts []*models.SteamAccount) {
	fmt.Println()
	color.Cyan("┌────┬─────────────────────┬─────────────────────┬──────────┬─────────────────┐")
	color.Cyan("│ ID │      用户名         │      SteamID        │   状态   │      备注       │")
	color.Cyan("├────┼─────────────────────┼─────────────────────┼──────────┼─────────────────┤")
	
	for _, account := range accounts {
		statusColor := color.New(color.FgWhite)
		switch account.Status {
		case "正常":
			statusColor = color.New(color.FgGreen)
		case "VAC封禁", "游戏封禁":
			statusColor = color.New(color.FgRed)
		case "未知":
			statusColor = color.New(color.FgYellow)
		}
		
		steamID := account.SteamID
		if steamID == "" {
			steamID = "未获取"
		}
		
		notes := account.Notes
		if len(notes) > 15 {
			notes = notes[:12] + "..."
		}
		
		username := account.Username
		if len(username) > 19 {
			username = username[:16] + "..."
		}
		
		if len(steamID) > 19 {
			steamID = steamID[:16] + "..."
		}
		
		fmt.Printf("│%3d │ %-19s │ %-19s │ ", account.ID, username, steamID)
		statusColor.Printf("%-8s", account.Status)
		fmt.Printf(" │ %-15s │\n", notes)
	}
	
	color.Cyan("└────┴─────────────────────┴─────────────────────┴──────────┴─────────────────┘")
}

// waitForEnter 等待用户按回车
func (ui *TerminalUI) waitForEnter() {
	color.White("\n按回车键继续...")
	fmt.Scanln()
}

// showGoodbye 显示退出信息
func (ui *TerminalUI) showGoodbye() {
	ui.clearScreen()
	color.Green("感谢使用 Steam账号管理工具 - 网络版!")
	color.White("再见! 👋")
	fmt.Println()
}
