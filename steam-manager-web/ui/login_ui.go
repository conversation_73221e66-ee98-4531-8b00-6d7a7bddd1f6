package ui

import (
	"fmt"
	"strings"

	"github.com/fatih/color"
	"github.com/manifoldco/promptui"
	"steam-manager-web/services"
)

// LoginUI 登录界面
type LoginUI struct {
	authService *services.AuthService
}

// NewLoginUI 创建登录界面
func NewLoginUI(authService *services.AuthService) *LoginUI {
	return &LoginUI{
		authService: authService,
	}
}

// Run 运行登录界面
func (ui *LoginUI) Run() bool {
	for {
		choice := ui.showLoginMenu()
		
		switch choice {
		case "login":
			if ui.handleLogin() {
				return true
			}
		case "register":
			if ui.handleRegister() {
				return true
			}
		case "local":
			// 本地模式，不需要登录
			return true
		case "exit":
			return false
		}
	}
}

// showLoginMenu 显示登录菜单
func (ui *LoginUI) showLoginMenu() string {
	color.<PERSON>an("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.<PERSON><PERSON>("║                        Steam账号管理工具 - 网络版                           ║")
	color.<PERSON><PERSON>("║                              用户登录                                       ║")
	color.<PERSON>an("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	
	items := []string{
		"登录账号",
		"注册账号",
		"本地模式 (无云端同步)",
		"退出程序",
	}
	
	prompt := promptui.Select{
		Label: "请选择操作",
		Items: items,
		Size:  len(items),
	}
	
	index, _, err := prompt.Run()
	if err != nil {
		return "exit"
	}
	
	switch index {
	case 0:
		return "login"
	case 1:
		return "register"
	case 2:
		return "local"
	case 3:
		return "exit"
	default:
		return "exit"
	}
}

// handleLogin 处理登录
func (ui *LoginUI) handleLogin() bool {
	color.Cyan("\n═══ 用户登录 ═══")
	
	// 输入用户名
	usernamePrompt := promptui.Prompt{
		Label: "用户名",
		Validate: func(input string) error {
			if len(strings.TrimSpace(input)) == 0 {
				return fmt.Errorf("用户名不能为空")
			}
			return nil
		},
	}
	
	username, err := usernamePrompt.Run()
	if err != nil {
		color.Red("输入取消")
		return false
	}
	
	// 输入密码
	passwordPrompt := promptui.Prompt{
		Label: "密码",
		Mask:  '*',
		Validate: func(input string) error {
			if len(strings.TrimSpace(input)) == 0 {
				return fmt.Errorf("密码不能为空")
			}
			return nil
		},
	}
	
	password, err := passwordPrompt.Run()
	if err != nil {
		color.Red("输入取消")
		return false
	}
	
	// 执行登录
	color.Yellow("正在登录...")
	user, err := ui.authService.Login(username, password)
	if err != nil {
		color.Red("登录失败: %v", err)
		fmt.Println()
		return false
	}
	
	color.Green("登录成功! 欢迎回来, %s", user.Username)
	if user.IsPremium {
		color.Green("🌟 您是高级版用户")
	} else {
		color.Yellow("👤 您是免费版用户")
	}
	
	// 保存登录状态
	ui.authService.SaveLoginState(user)
	
	fmt.Println()
	color.White("按回车键继续...")
	fmt.Scanln()
	
	return true
}

// handleRegister 处理注册
func (ui *LoginUI) handleRegister() bool {
	color.Cyan("\n═══ 用户注册 ═══")
	
	// 输入用户名
	usernamePrompt := promptui.Prompt{
		Label: "用户名 (3-20个字符)",
		Validate: func(input string) error {
			input = strings.TrimSpace(input)
			if len(input) < 3 || len(input) > 20 {
				return fmt.Errorf("用户名长度必须在3-20个字符之间")
			}
			return nil
		},
	}
	
	username, err := usernamePrompt.Run()
	if err != nil {
		color.Red("输入取消")
		return false
	}
	
	// 输入邮箱
	emailPrompt := promptui.Prompt{
		Label: "邮箱地址",
		Validate: func(input string) error {
			input = strings.TrimSpace(input)
			if len(input) == 0 {
				return fmt.Errorf("邮箱不能为空")
			}
			if !strings.Contains(input, "@") {
				return fmt.Errorf("请输入有效的邮箱地址")
			}
			return nil
		},
	}
	
	email, err := emailPrompt.Run()
	if err != nil {
		color.Red("输入取消")
		return false
	}
	
	// 输入密码
	passwordPrompt := promptui.Prompt{
		Label: "密码 (至少6个字符)",
		Mask:  '*',
		Validate: func(input string) error {
			if len(input) < 6 {
				return fmt.Errorf("密码长度至少6个字符")
			}
			return nil
		},
	}
	
	password, err := passwordPrompt.Run()
	if err != nil {
		color.Red("输入取消")
		return false
	}
	
	// 确认密码
	confirmPrompt := promptui.Prompt{
		Label: "确认密码",
		Mask:  '*',
		Validate: func(input string) error {
			if input != password {
				return fmt.Errorf("两次输入的密码不一致")
			}
			return nil
		},
	}
	
	_, err = confirmPrompt.Run()
	if err != nil {
		color.Red("密码确认失败")
		return false
	}
	
	// 执行注册
	color.Yellow("正在注册...")
	user, err := ui.authService.Register(username, email, password)
	if err != nil {
		color.Red("注册失败: %v", err)
		fmt.Println()
		return false
	}
	
	color.Green("注册成功! 欢迎使用, %s", user.Username)
	color.Yellow("👤 您当前是免费版用户")
	color.White("可以在设置中升级到高级版以享受云端同步等功能")
	
	// 保存登录状态
	ui.authService.SaveLoginState(user)
	
	fmt.Println()
	color.White("按回车键继续...")
	fmt.Scanln()
	
	return true
}
