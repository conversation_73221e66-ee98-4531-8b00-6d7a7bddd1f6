package ui

import (
	"bufio"
	"fmt"
	"os"
	"strings"

	"github.com/fatih/color"
	"github.com/manifoldco/promptui"
	"steam-manager-web/models"
)

// getKeyInput 获取键盘输入
func (ui *TerminalUI) getKeyInput() string {
	reader := bufio.NewReader(os.Stdin)
	input, _ := reader.ReadString('\n')
	input = strings.TrimSpace(strings.ToLower(input))
	
	switch input {
	case "a":
		return "add"
	case "d":
		return "delete"
	case "l":
		return "login"
	case "m":
		return "menu"
	case "q", "esc", "exit":
		return "escape"
	default:
		return input
	}
}

// addAccount 添加账号
func (ui *TerminalUI) addAccount() {
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                              添加账号                                       ║")
	color.<PERSON>an("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	
	// 输入用户名
	usernamePrompt := promptui.Prompt{
		Label: "Steam用户名",
		Validate: func(input string) error {
			if len(strings.TrimSpace(input)) == 0 {
				return fmt.Errorf("用户名不能为空")
			}
			return nil
		},
	}
	
	username, err := usernamePrompt.Run()
	if err != nil {
		color.Red("输入取消")
		ui.waitForEnter()
		return
	}
	
	// 输入密码
	passwordPrompt := promptui.Prompt{
		Label: "Steam密码",
		Mask:  '*',
		Validate: func(input string) error {
			if len(strings.TrimSpace(input)) == 0 {
				return fmt.Errorf("密码不能为空")
			}
			return nil
		},
	}
	
	password, err := passwordPrompt.Run()
	if err != nil {
		color.Red("输入取消")
		ui.waitForEnter()
		return
	}
	
	// 输入备注
	notesPrompt := promptui.Prompt{
		Label: "备注 (可选)",
	}
	
	notes, _ := notesPrompt.Run()
	
	// 添加账号
	account := &models.SteamAccount{
		Username: username,
		Password: password,
		Notes:    notes,
	}
	
	if ui.currentUser != nil {
		account.UserID = ui.currentUser.ID
	}
	
	err = ui.accountService.AddAccount(account)
	if err != nil {
		color.Red("添加账号失败: %v", err)
	} else {
		color.Green("账号添加成功!")
	}
	
	ui.waitForEnter()
}

// deleteAccount 删除账号
func (ui *TerminalUI) deleteAccount(accounts []*models.SteamAccount) {
	if len(accounts) == 0 {
		color.Yellow("没有可删除的账号")
		ui.waitForEnter()
		return
	}
	
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                              删除账号                                       ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	
	// 显示账号列表供选择
	items := make([]string, len(accounts))
	for i, account := range accounts {
		items[i] = fmt.Sprintf("%d. %s (%s)", account.ID, account.Username, account.Notes)
	}
	
	prompt := promptui.Select{
		Label: "选择要删除的账号",
		Items: items,
		Size:  10,
	}
	
	index, _, err := prompt.Run()
	if err != nil {
		color.Red("选择取消")
		ui.waitForEnter()
		return
	}
	
	selectedAccount := accounts[index]
	
	// 确认删除
	confirmPrompt := promptui.Prompt{
		Label:     fmt.Sprintf("确定要删除账号 '%s' 吗? (y/N)", selectedAccount.Username),
		IsConfirm: true,
	}
	
	_, err = confirmPrompt.Run()
	if err != nil {
		color.Yellow("删除取消")
		ui.waitForEnter()
		return
	}
	
	// 执行删除
	err = ui.accountService.DeleteAccount(selectedAccount.ID)
	if err != nil {
		color.Red("删除账号失败: %v", err)
	} else {
		color.Green("账号删除成功!")
	}
	
	ui.waitForEnter()
}

// loginSteam Steam登录
func (ui *TerminalUI) loginSteam(accounts []*models.SteamAccount) {
	if len(accounts) == 0 {
		color.Yellow("没有可登录的账号")
		ui.waitForEnter()
		return
	}
	
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                            Steam 登录                                       ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	
	// 显示账号列表供选择
	items := make([]string, len(accounts))
	for i, account := range accounts {
		status := account.Status
		if status == "" {
			status = "未知"
		}
		items[i] = fmt.Sprintf("%s [%s] - %s", account.Username, status, account.Notes)
	}
	
	prompt := promptui.Select{
		Label: "选择要登录的账号",
		Items: items,
		Size:  10,
	}
	
	index, _, err := prompt.Run()
	if err != nil {
		color.Red("选择取消")
		ui.waitForEnter()
		return
	}
	
	selectedAccount := accounts[index]
	
	// 执行Steam登录
	color.Yellow("正在启动Steam登录...")
	err = ui.steamService.LoginSteam(selectedAccount.Username, selectedAccount.Password)
	if err != nil {
		color.Red("Steam登录失败: %v", err)
	} else {
		color.Green("Steam登录成功!")
		
		// 询问是否启动游戏
		gamePrompt := promptui.Prompt{
			Label:     "是否启动PUBG? (Y/n)",
			IsConfirm: true,
		}
		
		_, err = gamePrompt.Run()
		if err == nil {
			color.Yellow("正在启动PUBG...")
			err = ui.steamService.LaunchGame("578080") // PUBG的Steam ID
			if err != nil {
				color.Red("启动游戏失败: %v", err)
			} else {
				color.Green("游戏启动成功!")
			}
		}
	}
	
	ui.waitForEnter()
}

// showMoreMenu 显示更多菜单
func (ui *TerminalUI) showMoreMenu() {
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                              更多功能                                       ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	
	menuItems := []string{
		"批量导入账号",
		"检查封禁状态",
		"账号统计",
		"用户设置",
	}
	
	if ui.currentUser != nil {
		menuItems = append(menuItems, "订阅管理")
		if !ui.currentUser.IsPremium {
			menuItems = append(menuItems, "升级到高级版")
		}
	}
	
	menuItems = append(menuItems, "返回主菜单")
	
	prompt := promptui.Select{
		Label: "选择功能",
		Items: menuItems,
		Size:  len(menuItems),
	}
	
	_, result, err := prompt.Run()
	if err != nil {
		return
	}
	
	switch result {
	case "批量导入账号":
		ui.importAccounts()
	case "检查封禁状态":
		ui.checkBanStatus()
	case "账号统计":
		ui.showAccountStats()
	case "用户设置":
		ui.showUserSettings()
	case "订阅管理":
		ui.showSubscriptionManagement()
	case "升级到高级版":
		ui.showUpgrade()
	case "返回主菜单":
		return
	}
}

// importAccounts 批量导入账号
func (ui *TerminalUI) importAccounts() {
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                            批量导入账号                                     ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	color.White("请输入账号信息，格式: 用户名:密码:备注")
	color.White("每行一个账号，输入空行结束:")
	fmt.Println()
	
	var accounts []*models.SteamAccount
	scanner := bufio.NewScanner(os.Stdin)
	
	for {
		fmt.Print("> ")
		if !scanner.Scan() {
			break
		}
		
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			break
		}
		
		parts := strings.Split(line, ":")
		if len(parts) < 2 {
			color.Red("格式错误，跳过: %s", line)
			continue
		}
		
		account := &models.SteamAccount{
			Username: parts[0],
			Password: parts[1],
		}
		
		if len(parts) > 2 {
			account.Notes = parts[2]
		}
		
		if ui.currentUser != nil {
			account.UserID = ui.currentUser.ID
		}
		
		accounts = append(accounts, account)
	}
	
	if len(accounts) == 0 {
		color.Yellow("没有输入任何账号")
		ui.waitForEnter()
		return
	}
	
	// 批量添加账号
	successCount := 0
	for _, account := range accounts {
		err := ui.accountService.AddAccount(account)
		if err != nil {
			color.Red("添加账号 %s 失败: %v", account.Username, err)
		} else {
			successCount++
		}
	}
	
	color.Green("成功导入 %d/%d 个账号", successCount, len(accounts))
	ui.waitForEnter()
}

// checkBanStatus 检查封禁状态
func (ui *TerminalUI) checkBanStatus() {
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                            检查封禁状态                                     ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	color.Yellow("正在检查所有账号的封禁状态...")
	
	// 这里应该调用封禁检测服务
	// 暂时显示提示信息
	color.White("封禁检测功能开发中...")
	
	ui.waitForEnter()
}

// showAccountStats 显示账号统计
func (ui *TerminalUI) showAccountStats() {
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                              账号统计                                       ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	
	stats, err := ui.accountService.GetAccountStats()
	if err != nil {
		color.Red("获取统计信息失败: %v", err)
		ui.waitForEnter()
		return
	}
	
	color.White("总账号数: %d", stats.Total)
	color.Green("正常账号: %d", stats.Normal)
	color.Red("VAC封禁: %d", stats.VACBan)
	color.Red("游戏封禁: %d", stats.GameBan)
	color.Yellow("未知状态: %d", stats.Unknown)
	
	ui.waitForEnter()
}

// showUserSettings 显示用户设置
func (ui *TerminalUI) showUserSettings() {
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                              用户设置                                       ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	color.White("用户设置功能开发中...")
	
	ui.waitForEnter()
}

// showSubscriptionManagement 显示订阅管理
func (ui *TerminalUI) showSubscriptionManagement() {
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                              订阅管理                                       ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	color.White("订阅管理功能开发中...")
	
	ui.waitForEnter()
}

// showUpgrade 显示升级页面
func (ui *TerminalUI) showUpgrade() {
	ui.clearScreen()
	
	color.Cyan("╔══════════════════════════════════════════════════════════════════════════════╗")
	color.Cyan("║                            升级到高级版                                     ║")
	color.Cyan("╚══════════════════════════════════════════════════════════════════════════════╝")
	
	fmt.Println()
	color.Yellow("高级版功能:")
	color.White("• 云端数据存储")
	color.White("• 多设备数据同步")
	color.White("• 无限账号数量")
	color.White("• 高级统计功能")
	color.White("• 优先技术支持")
	fmt.Println()
	color.Green("价格: ¥20/年")
	fmt.Println()
	color.White("升级功能开发中...")
	
	ui.waitForEnter()
}
