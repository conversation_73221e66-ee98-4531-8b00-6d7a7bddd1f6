@echo off
echo Building Steam Manager Web...
echo.

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go is not installed or not in PATH
    echo Please install Go 1.23+ from https://golang.org/
    pause
    exit /b 1
)

REM 创建构建目录
if not exist "build" mkdir build

REM 检查依赖
echo Checking dependencies...
go mod tidy

REM 构建Windows版本
echo Building for Windows...
set GOOS=windows
set GOARCH=amd64
go build -o build/steam-manager-web.exe main.go

REM 构建Linux版本
echo Building for Linux...
set GOOS=linux
set GOARCH=amd64
go build -o build/steam-manager-web-linux main.go

REM 复制静态文件
echo Copying static files...
xcopy /E /I /Y static build\static
copy config.example.json build\config.json

echo.
echo Build completed! Files are in the 'build' directory.
echo.
pause
