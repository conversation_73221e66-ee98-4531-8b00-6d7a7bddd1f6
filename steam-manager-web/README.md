# Steam账号管理工具 - 网络版

基于 `steam-manager-simple` 项目开发的网络版本，支持用户管理、云端存储、订阅服务等功能。

## 🚀 功能特性

### 🔐 用户系统
- **用户注册/登录** - JWT认证，安全可靠
- **个人信息管理** - 用户资料修改、密码更改
- **权限控制** - 免费用户和付费用户权限区分

### ☁️ 云端存储
- **免费版** - 本地数据存储功能
- **付费版** - 云端数据存储，多设备同步
- **数据安全** - 加密存储，保护用户隐私

### 💰 订阅服务
- **年费制** - 20元/年的付费订阅
- **订单管理** - 完整的订单创建、支付、取消流程
- **订阅管理** - 订阅状态查看、续费提醒

### 🎮 Steam功能
- **账号管理** - 添加、编辑、删除Steam账号
- **批量导入** - 支持批量导入账号
- **SteamID获取** - 自动获取和管理SteamID
- **封禁检测** - VAC和游戏封禁状态检测
- **统计分析** - 账号状态统计和分析

## 🛠️ 技术栈

### 后端
- **Go 1.23** - 主要编程语言
- **Gin** - Web框架
- **MySQL** - 数据库
- **JWT** - 用户认证
- **bcrypt** - 密码加密

### 前端
- **HTML5/CSS3** - 页面结构和样式
- **JavaScript** - 交互逻辑
- **响应式设计** - 适配不同设备

## 📦 安装部署

### 环境要求
- Go 1.23+
- MySQL 5.7+
- 现代浏览器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd steam-manager-web
```

2. **安装依赖**
```bash
go mod tidy
```

3. **配置数据库**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE steam_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

4. **配置文件**
```bash
# 首次运行会自动创建 config.json
# 根据需要修改数据库连接信息
```

5. **运行程序**
```bash
go run main.go
```

6. **访问应用**
```
http://localhost:8080
```

## 📁 项目结构

```
steam-manager-web/
├── main.go              # 程序入口
├── config/              # 配置管理
│   └── config.go
├── models/              # 数据模型
│   ├── user.go
│   ├── account.go
│   └── order.go
├── handlers/            # HTTP处理器
│   ├── auth.go
│   ├── accounts.go
│   ├── steam.go
│   └── orders.go
├── middleware/          # 中间件
│   ├── auth.go
│   └── cors.go
├── services/            # 业务逻辑
│   └── steam.go
├── database/            # 数据库
│   └── mysql.go
├── utils/               # 工具函数
│   ├── jwt.go
│   ├── password.go
│   └── response.go
├── static/              # 静态文件
│   ├── css/
│   ├── js/
│   └── index.html
├── config.json          # 配置文件
├── go.mod
└── README.md
```

## 🔧 配置说明

### 数据库配置
```json
{
  "database": {
    "host": "localhost",
    "port": "3306",
    "username": "root",
    "password": "password",
    "database": "steam_manager",
    "charset": "utf8mb4"
  }
}
```

### JWT配置
```json
{
  "jwt": {
    "secret": "your-secret-key-change-this-in-production",
    "expire_hour": 168
  }
}
```

### Steam API配置
```json
{
  "steam": {
    "api_keys": [
      "your-steam-api-key-1",
      "your-steam-api-key-2"
    ]
  }
}
```

## 📊 数据库表结构

### 用户表 (users)
- id - 用户ID
- username - 用户名
- email - 邮箱
- password - 密码（加密）
- is_premium - 是否为付费用户
- expire_time - 会员过期时间
- created_at - 创建时间
- updated_at - 更新时间

### Steam账号表 (steam_accounts)
- id - 账号ID
- user_id - 用户ID
- username - Steam用户名
- password - Steam密码
- steam_id - SteamID
- pubg_ban_status - 封禁状态
- last_login - 最后登录时间
- last_checked - 最后检查时间
- notes - 备注
- created_at - 创建时间
- updated_at - 更新时间

### 订单表 (orders)
- id - 订单ID
- user_id - 用户ID
- order_no - 订单号
- amount - 金额
- status - 订单状态
- pay_method - 支付方式
- pay_time - 支付时间
- expire_time - 过期时间
- description - 订单描述
- created_at - 创建时间
- updated_at - 更新时间

### 订阅表 (subscriptions)
- id - 订阅ID
- user_id - 用户ID
- start_time - 开始时间
- end_time - 结束时间
- is_active - 是否激活
- order_id - 关联订单ID
- created_at - 创建时间
- updated_at - 更新时间

## 🔗 API接口

### 认证相关
- `POST /api/v1/register` - 用户注册
- `POST /api/v1/login` - 用户登录
- `GET /api/v1/profile` - 获取用户信息
- `PUT /api/v1/profile` - 更新用户信息
- `POST /api/v1/change-password` - 修改密码

### 账号管理
- `GET /api/v1/accounts` - 获取账号列表
- `POST /api/v1/accounts` - 添加账号
- `PUT /api/v1/accounts/:id` - 更新账号
- `DELETE /api/v1/accounts/:id` - 删除账号
- `POST /api/v1/accounts/import` - 批量导入账号
- `GET /api/v1/accounts/stats` - 获取账号统计

### Steam功能
- `POST /api/v1/steam/login` - Steam登录
- `POST /api/v1/steam/accounts/:id/check-steamid` - 检查SteamID
- `POST /api/v1/steam/accounts/:id/check-ban` - 检查封禁状态
- `POST /api/v1/steam/batch-check-steamid` - 批量检查SteamID
- `POST /api/v1/steam/batch-check-ban` - 批量检查封禁状态

### 订单和订阅
- `POST /api/v1/orders` - 创建订单
- `GET /api/v1/orders` - 获取订单列表
- `GET /api/v1/orders/:id` - 获取订单详情
- `POST /api/v1/orders/:id/pay` - 支付订单
- `POST /api/v1/orders/:id/cancel` - 取消订单
- `GET /api/v1/subscription` - 获取订阅信息

## 🚀 部署建议

### 生产环境
1. 修改JWT密钥为随机字符串
2. 使用HTTPS协议
3. 配置防火墙和安全组
4. 定期备份数据库
5. 监控系统资源使用情况

### 性能优化
1. 使用连接池优化数据库连接
2. 添加Redis缓存热点数据
3. 使用CDN加速静态资源
4. 配置Nginx反向代理

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交Issue和Pull Request来帮助改进项目！

## 📞 支持

如果您在使用过程中遇到问题，请：
1. 查看本README的相关说明
2. 检查项目的Issue页面
3. 提交新的Issue描述问题

---

**注意**: 本工具仅供学习和研究使用，请遵守Steam服务条款和相关法律法规。
