package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os/exec"
	"runtime"
	"time"

	"steam-manager-web/config"
	"steam-manager-web/database"
)

// SteamAPIResponse Steam API响应结构
type SteamAPIResponse struct {
	Response struct {
		Success int    `json:"success"`
		SteamID string `json:"steamid"`
		Message string `json:"message"`
	} `json:"response"`
}

// BanStatusResponse 封禁状态响应
type BanStatusResponse struct {
	Players []struct {
		SteamID          string `json:"SteamId"`
		CommunityBanned  bool   `json:"CommunityBanned"`
		VACBanned        bool   `json:"VACBanned"`
		NumberOfVACBans  int    `json:"NumberOfVACBans"`
		DaysSinceLastBan int    `json:"DaysSinceLastBan"`
		NumberOfGameBans int    `json:"NumberOfGameBans"`
		EconomyBan       string `json:"EconomyBan"`
	} `json:"players"`
}

// SteamService Steam服务
type SteamService struct {
	apiKeyIndex int
}

// NewSteamService 创建Steam服务
func NewSteamService() *SteamService {
	return &SteamService{
		apiKeyIndex: 0,
	}
}

// getNextAPIKey 获取下一个API密钥
func (s *SteamService) getNextAPIKey() string {
	if config.AppConfig == nil || len(config.AppConfig.Steam.APIKeys) == 0 {
		return ""
	}

	key := config.AppConfig.Steam.APIKeys[s.apiKeyIndex]
	s.apiKeyIndex = (s.apiKeyIndex + 1) % len(config.AppConfig.Steam.APIKeys)
	return key
}

// LoginSteam Steam登录
func (s *SteamService) LoginSteam(username, password string) error {
	// 实现Steam登录逻辑
	// 这里可以调用Steam客户端或使用Steam API

	if runtime.GOOS == "windows" {
		// Windows下启动Steam
		cmd := exec.Command("steam", "-login", username, password)
		return cmd.Start()
	}

	return fmt.Errorf("当前系统不支持Steam登录")
}

// LaunchGame 启动游戏
func (s *SteamService) LaunchGame(gameID string) error {
	if runtime.GOOS == "windows" {
		// 使用Steam URL协议启动游戏
		steamURL := fmt.Sprintf("steam://rungameid/%s", gameID)
		cmd := exec.Command("cmd", "/c", "start", steamURL)
		return cmd.Run()
	}

	return fmt.Errorf("当前系统不支持游戏启动")
}

// GetSteamIDByAccount 通过账号密码获取SteamID
func (s *SteamService) GetSteamIDByAccount(username, password string) (string, error) {
	// 这里应该实现实际的Steam登录逻辑
	// 由于Steam登录比较复杂，这里返回一个模拟的SteamID
	// 在实际项目中，需要实现完整的Steam登录流程

	// 模拟延迟
	time.Sleep(1 * time.Second)

	// 返回模拟的SteamID（实际应该通过Steam登录获取）
	return fmt.Sprintf("*********%08d", len(username)), nil
}

// ResolveSteamID 通过用户名解析SteamID
func (s *SteamService) ResolveSteamID(username string) (string, error) {
	apiKey := s.getNextAPIKey()
	if apiKey == "" {
		return "", fmt.Errorf("no Steam API key available")
	}

	// 构建API URL
	apiURL := fmt.Sprintf("https://api.steampowered.com/ISteamUser/ResolveVanityURL/v0001/?key=%s&vanityurl=%s",
		apiKey, url.QueryEscape(username))

	// 发送HTTP请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Get(apiURL)
	if err != nil {
		return "", fmt.Errorf("failed to call Steam API: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	// 解析JSON响应
	var apiResp SteamAPIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return "", fmt.Errorf("failed to parse JSON response: %v", err)
	}

	// 检查响应状态
	if apiResp.Response.Success != 1 {
		if apiResp.Response.Success == 42 {
			return "", fmt.Errorf("user has not set a vanity URL")
		}
		return "", fmt.Errorf("Steam API error: %s", apiResp.Response.Message)
	}

	return apiResp.Response.SteamID, nil
}

// CheckBanStatus 检查封禁状态
func (s *SteamService) CheckBanStatus(steamID string) (int, error) {
	if steamID == "" {
		return -1, fmt.Errorf("SteamID is empty")
	}

	apiKey := s.getNextAPIKey()
	if apiKey == "" {
		return -1, fmt.Errorf("no Steam API key available")
	}

	// 构建API URL
	apiURL := fmt.Sprintf("https://api.steampowered.com/ISteamUser/GetPlayerBans/v1/?key=%s&steamids=%s",
		apiKey, steamID)

	// 发送HTTP请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Get(apiURL)
	if err != nil {
		return -1, fmt.Errorf("failed to call Steam API: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return -1, fmt.Errorf("failed to read response: %v", err)
	}

	// 解析JSON响应
	var banResp BanStatusResponse
	if err := json.Unmarshal(body, &banResp); err != nil {
		return -1, fmt.Errorf("failed to parse JSON response: %v", err)
	}

	// 检查是否有玩家数据
	if len(banResp.Players) == 0 {
		return -1, fmt.Errorf("no player data found")
	}

	player := banResp.Players[0]

	// 判断封禁状态
	if player.VACBanned {
		return 1, nil // VAC封禁
	}
	if player.NumberOfGameBans > 0 {
		return 2, nil // 游戏封禁
	}

	return 0, nil // 正常
}

// UpdateAccountSteamID 更新账号的SteamID
func UpdateAccountSteamID(accountID int, steamID string) error {
	_, err := database.DB.Exec("UPDATE steam_accounts SET steam_id = ?, updated_at = ? WHERE id = ?",
		steamID, time.Now(), accountID)
	return err
}

// UpdateAccountBanStatus 更新账号的封禁状态
func UpdateAccountBanStatus(accountID int, banStatus int) error {
	_, err := database.DB.Exec("UPDATE steam_accounts SET pubg_ban_status = ?, last_checked = ?, updated_at = ? WHERE id = ?",
		banStatus, time.Now(), time.Now(), accountID)
	return err
}

// BatchCheckBanStatus 批量检查封禁状态
func BatchCheckBanStatus(userID int) error {
	// 获取需要检查的账号（有SteamID但超过1小时未检查的）
	rows, err := database.DB.Query(`
		SELECT id, steam_id 
		FROM steam_accounts 
		WHERE user_id = ? AND steam_id != '' AND (last_checked IS NULL OR last_checked < ?)
		ORDER BY last_login DESC
		LIMIT 10
	`, userID, time.Now().Add(-1*time.Hour))

	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var accountID int
		var steamID string

		if err := rows.Scan(&accountID, &steamID); err != nil {
			continue
		}

		// 检查封禁状态
		steamService := NewSteamService()
		banStatus, err := steamService.CheckBanStatus(steamID)
		if err != nil {
			continue
		}

		// 更新数据库
		UpdateAccountBanStatus(accountID, banStatus)

		// 避免API调用过于频繁
		time.Sleep(500 * time.Millisecond)
	}

	return nil
}

// BatchGetSteamID 批量获取SteamID
func BatchGetSteamID(userID int) error {
	// 获取没有SteamID的账号
	rows, err := database.DB.Query(`
		SELECT id, username, password 
		FROM steam_accounts 
		WHERE user_id = ? AND (steam_id = '' OR steam_id IS NULL)
		LIMIT 5
	`, userID)

	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var accountID int
		var username, password string

		if err := rows.Scan(&accountID, &username, &password); err != nil {
			continue
		}

		// 尝试通过用户名解析SteamID
		steamService := NewSteamService()
		steamID, err := steamService.ResolveSteamID(username)
		if err != nil {
			// 如果解析失败，尝试通过账号密码获取
			steamID, err = steamService.GetSteamIDByAccount(username, password)
			if err != nil {
				continue
			}
		}

		// 更新数据库
		UpdateAccountSteamID(accountID, steamID)

		// 获取到SteamID后立即检查封禁状态
		banStatus, err := steamService.CheckBanStatus(steamID)
		if err == nil {
			UpdateAccountBanStatus(accountID, banStatus)
		}

		// 避免API调用过于频繁
		time.Sleep(1 * time.Second)
	}

	return nil
}
