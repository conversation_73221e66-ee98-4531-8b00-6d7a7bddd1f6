package services

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"steam-manager-web/database"
	"steam-manager-web/models"
	"steam-manager-web/utils"
)

// AuthService 认证服务
type AuthService struct {
	useDatabase bool
	currentUser *models.User
	tokenFile   string
}

// NewAuthService 创建认证服务
func NewAuthService(useDatabase bool) *AuthService {
	// 获取用户主目录
	homeDir, _ := os.UserHomeDir()
	tokenFile := filepath.Join(homeDir, ".steam-manager-token")
	
	service := &AuthService{
		useDatabase: useDatabase,
		tokenFile:   tokenFile,
	}
	
	// 尝试从本地文件加载登录状态
	service.loadLoginState()
	
	return service
}

// Login 用户登录
func (s *AuthService) Login(username, password string) (*models.User, error) {
	if !s.useDatabase || database.DB == nil {
		return nil, fmt.Errorf("数据库未连接，无法登录")
	}
	
	// 查询用户
	var user models.User
	err := database.DB.QueryRow(
		"SELECT id, username, email, password, is_premium, expire_time FROM users WHERE username = ?",
		username,
	).Scan(&user.ID, &user.Username, &user.Email, &user.Password, &user.IsPremium, &user.ExpireTime)
	
	if err == sql.ErrNoRows {
		return nil, fmt.Errorf("用户名或密码错误")
	}
	if err != nil {
		return nil, fmt.Errorf("数据库查询错误: %v", err)
	}
	
	// 验证密码
	if !utils.CheckPassword(password, user.Password) {
		return nil, fmt.Errorf("用户名或密码错误")
	}
	
	// 检查会员是否过期
	if user.IsPremium && user.ExpireTime != nil && time.Now().After(*user.ExpireTime) {
		user.IsPremium = false
		// 更新数据库中的会员状态
		database.DB.Exec("UPDATE users SET is_premium = FALSE WHERE id = ?", user.ID)
	}
	
	s.currentUser = &user
	return &user, nil
}

// Register 用户注册
func (s *AuthService) Register(username, email, password string) (*models.User, error) {
	if !s.useDatabase || database.DB == nil {
		return nil, fmt.Errorf("数据库未连接，无法注册")
	}
	
	// 检查用户名是否已存在
	var count int
	err := database.DB.QueryRow("SELECT COUNT(*) FROM users WHERE username = ?", username).Scan(&count)
	if err != nil {
		return nil, fmt.Errorf("数据库查询错误: %v", err)
	}
	if count > 0 {
		return nil, fmt.Errorf("用户名已存在")
	}
	
	// 检查邮箱是否已存在
	err = database.DB.QueryRow("SELECT COUNT(*) FROM users WHERE email = ?", email).Scan(&count)
	if err != nil {
		return nil, fmt.Errorf("数据库查询错误: %v", err)
	}
	if count > 0 {
		return nil, fmt.Errorf("邮箱已存在")
	}
	
	// 加密密码
	hashedPassword, err := utils.HashPassword(password)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %v", err)
	}
	
	// 创建用户
	now := time.Now()
	result, err := database.DB.Exec(
		"INSERT INTO users (username, email, password, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
		username, email, hashedPassword, now, now,
	)
	if err != nil {
		return nil, fmt.Errorf("创建用户失败: %v", err)
	}
	
	userID, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("获取用户ID失败: %v", err)
	}
	
	user := &models.User{
		ID:        int(userID),
		Username:  username,
		Email:     email,
		IsPremium: false,
		CreatedAt: now,
		UpdatedAt: now,
	}
	
	s.currentUser = user
	return user, nil
}

// IsLoggedIn 检查是否已登录
func (s *AuthService) IsLoggedIn() bool {
	return s.currentUser != nil
}

// GetCurrentUser 获取当前用户
func (s *AuthService) GetCurrentUser() *models.User {
	return s.currentUser
}

// SaveLoginState 保存登录状态到本地文件
func (s *AuthService) SaveLoginState(user *models.User) error {
	if !s.useDatabase {
		return nil // 本地模式不需要保存登录状态
	}
	
	// 生成token
	token, err := utils.GenerateToken(user.ID, user.Username)
	if err != nil {
		return err
	}
	
	// 保存到文件
	loginData := map[string]interface{}{
		"token":      token,
		"user_id":    user.ID,
		"username":   user.Username,
		"email":      user.Email,
		"is_premium": user.IsPremium,
		"expire_time": user.ExpireTime,
		"saved_at":   time.Now(),
	}
	
	data, err := json.Marshal(loginData)
	if err != nil {
		return err
	}
	
	return os.WriteFile(s.tokenFile, data, 0600)
}

// loadLoginState 从本地文件加载登录状态
func (s *AuthService) loadLoginState() {
	if !s.useDatabase {
		return
	}
	
	data, err := os.ReadFile(s.tokenFile)
	if err != nil {
		return // 文件不存在或读取失败
	}
	
	var loginData map[string]interface{}
	if err := json.Unmarshal(data, &loginData); err != nil {
		return
	}
	
	// 验证token
	token, ok := loginData["token"].(string)
	if !ok {
		return
	}
	
	claims, err := utils.ParseToken(token)
	if err != nil {
		// Token无效，删除文件
		os.Remove(s.tokenFile)
		return
	}
	
	// 从数据库重新获取用户信息
	if database.DB != nil {
		var user models.User
		err := database.DB.QueryRow(
			"SELECT id, username, email, is_premium, expire_time, created_at, updated_at FROM users WHERE id = ?",
			claims.UserID,
		).Scan(&user.ID, &user.Username, &user.Email, &user.IsPremium, &user.ExpireTime, &user.CreatedAt, &user.UpdatedAt)
		
		if err == nil {
			// 检查会员是否过期
			if user.IsPremium && user.ExpireTime != nil && time.Now().After(*user.ExpireTime) {
				user.IsPremium = false
				// 更新数据库中的会员状态
				database.DB.Exec("UPDATE users SET is_premium = FALSE WHERE id = ?", user.ID)
			}
			
			s.currentUser = &user
		}
	}
}

// Logout 用户登出
func (s *AuthService) Logout() {
	s.currentUser = nil
	// 删除本地token文件
	os.Remove(s.tokenFile)
}

// UpgradeToPremium 升级到高级版
func (s *AuthService) UpgradeToPremium() error {
	if !s.useDatabase || database.DB == nil || s.currentUser == nil {
		return fmt.Errorf("无法升级：数据库未连接或用户未登录")
	}
	
	// 设置会员到期时间为一年后
	expireTime := time.Now().AddDate(1, 0, 0)
	
	_, err := database.DB.Exec(
		"UPDATE users SET is_premium = TRUE, expire_time = ?, updated_at = ? WHERE id = ?",
		expireTime, time.Now(), s.currentUser.ID,
	)
	
	if err != nil {
		return fmt.Errorf("升级失败: %v", err)
	}
	
	// 更新当前用户信息
	s.currentUser.IsPremium = true
	s.currentUser.ExpireTime = &expireTime
	
	return nil
}
