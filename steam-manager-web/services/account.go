package services

import (
	"database/sql"
	"fmt"
	"time"

	"steam-manager-web/database"
	"steam-manager-web/models"
)

// AccountService 账号服务
type AccountService struct {
	useDatabase bool
	localAccounts []*models.SteamAccount
	nextID int
}

// NewAccountService 创建账号服务
func NewAccountService(useDatabase bool) *AccountService {
	return &AccountService{
		useDatabase: useDatabase,
		localAccounts: make([]*models.SteamAccount, 0),
		nextID: 1,
	}
}

// GetAllAccounts 获取所有账号
func (s *AccountService) GetAllAccounts() ([]*models.SteamAccount, error) {
	if s.useDatabase && database.DB != nil {
		return s.getAccountsFromDB()
	}
	
	// 本地模式
	return s.localAccounts, nil
}

// getAccountsFromDB 从数据库获取账号
func (s *AccountService) getAccountsFromDB() ([]*models.SteamAccount, error) {
	rows, err := database.DB.Query(`
		SELECT id, user_id, username, password, steam_id, pubg_ban_status, last_login, last_checked, notes, created_at, updated_at
		FROM steam_accounts 
		ORDER BY id ASC
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var accounts []*models.SteamAccount
	for rows.Next() {
		var account models.SteamAccount
		var lastLogin, lastChecked sql.NullTime
		
		err := rows.Scan(
			&account.ID,
			&account.UserID,
			&account.Username,
			&account.Password,
			&account.SteamID,
			&account.PUBGBanStatus,
			&lastLogin,
			&lastChecked,
			&account.Notes,
			&account.CreatedAt,
			&account.UpdatedAt,
		)
		if err != nil {
			continue
		}
		
		if lastLogin.Valid {
			account.LastLogin = &lastLogin.Time
		}
		if lastChecked.Valid {
			account.LastChecked = &lastChecked.Time
		}
		
		// 设置状态
		switch account.PUBGBanStatus {
		case 0:
			account.Status = "正常"
		case 1:
			account.Status = "VAC封禁"
		case 2:
			account.Status = "游戏封禁"
		default:
			account.Status = "未知"
		}
		
		accounts = append(accounts, &account)
	}
	
	return accounts, nil
}

// AddAccount 添加账号
func (s *AccountService) AddAccount(account *models.SteamAccount) error {
	if s.useDatabase && database.DB != nil {
		return s.addAccountToDB(account)
	}
	
	// 本地模式
	account.ID = s.nextID
	s.nextID++
	account.CreatedAt = time.Now()
	account.UpdatedAt = time.Now()
	account.Status = "未知"
	
	s.localAccounts = append(s.localAccounts, account)
	return nil
}

// addAccountToDB 添加账号到数据库
func (s *AccountService) addAccountToDB(account *models.SteamAccount) error {
	now := time.Now()
	result, err := database.DB.Exec(`
		INSERT INTO steam_accounts (user_id, username, password, notes, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?)
	`, account.UserID, account.Username, account.Password, account.Notes, now, now)
	
	if err != nil {
		return err
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	
	account.ID = int(id)
	account.CreatedAt = now
	account.UpdatedAt = now
	account.Status = "未知"
	
	return nil
}

// DeleteAccount 删除账号
func (s *AccountService) DeleteAccount(accountID int) error {
	if s.useDatabase && database.DB != nil {
		return s.deleteAccountFromDB(accountID)
	}
	
	// 本地模式
	for i, account := range s.localAccounts {
		if account.ID == accountID {
			s.localAccounts = append(s.localAccounts[:i], s.localAccounts[i+1:]...)
			return nil
		}
	}
	
	return fmt.Errorf("账号不存在")
}

// deleteAccountFromDB 从数据库删除账号
func (s *AccountService) deleteAccountFromDB(accountID int) error {
	result, err := database.DB.Exec("DELETE FROM steam_accounts WHERE id = ?", accountID)
	if err != nil {
		return err
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("账号不存在")
	}
	
	return nil
}

// GetAccountStats 获取账号统计
func (s *AccountService) GetAccountStats() (*models.AccountStats, error) {
	accounts, err := s.GetAllAccounts()
	if err != nil {
		return nil, err
	}
	
	stats := &models.AccountStats{
		Total: len(accounts),
	}
	
	for _, account := range accounts {
		switch account.Status {
		case "正常":
			stats.Normal++
		case "VAC封禁":
			stats.VACBan++
		case "游戏封禁":
			stats.GameBan++
		default:
			stats.Unknown++
		}
	}
	
	return stats, nil
}

// UpdateAccount 更新账号
func (s *AccountService) UpdateAccount(account *models.SteamAccount) error {
	if s.useDatabase && database.DB != nil {
		return s.updateAccountInDB(account)
	}
	
	// 本地模式
	for i, acc := range s.localAccounts {
		if acc.ID == account.ID {
			account.UpdatedAt = time.Now()
			s.localAccounts[i] = account
			return nil
		}
	}
	
	return fmt.Errorf("账号不存在")
}

// updateAccountInDB 在数据库中更新账号
func (s *AccountService) updateAccountInDB(account *models.SteamAccount) error {
	_, err := database.DB.Exec(`
		UPDATE steam_accounts 
		SET username = ?, password = ?, notes = ?, updated_at = ?
		WHERE id = ?
	`, account.Username, account.Password, account.Notes, time.Now(), account.ID)
	
	return err
}

// GetAccountByID 根据ID获取账号
func (s *AccountService) GetAccountByID(accountID int) (*models.SteamAccount, error) {
	accounts, err := s.GetAllAccounts()
	if err != nil {
		return nil, err
	}
	
	for _, account := range accounts {
		if account.ID == accountID {
			return account, nil
		}
	}
	
	return nil, fmt.Errorf("账号不存在")
}

// UpdateAccountSteamID 更新账号的SteamID
func (s *AccountService) UpdateAccountSteamID(accountID int, steamID string) error {
	if s.useDatabase && database.DB != nil {
		_, err := database.DB.Exec("UPDATE steam_accounts SET steam_id = ?, updated_at = ? WHERE id = ?",
			steamID, time.Now(), accountID)
		return err
	}
	
	// 本地模式
	for _, account := range s.localAccounts {
		if account.ID == accountID {
			account.SteamID = steamID
			account.UpdatedAt = time.Now()
			return nil
		}
	}
	
	return fmt.Errorf("账号不存在")
}

// UpdateAccountBanStatus 更新账号的封禁状态
func (s *AccountService) UpdateAccountBanStatus(accountID int, banStatus int) error {
	if s.useDatabase && database.DB != nil {
		_, err := database.DB.Exec("UPDATE steam_accounts SET pubg_ban_status = ?, last_checked = ?, updated_at = ? WHERE id = ?",
			banStatus, time.Now(), time.Now(), accountID)
		return err
	}
	
	// 本地模式
	for _, account := range s.localAccounts {
		if account.ID == accountID {
			account.PUBGBanStatus = banStatus
			now := time.Now()
			account.LastChecked = &now
			account.UpdatedAt = now
			
			// 更新状态文本
			switch banStatus {
			case 0:
				account.Status = "正常"
			case 1:
				account.Status = "VAC封禁"
			case 2:
				account.Status = "游戏封禁"
			default:
				account.Status = "未知"
			}
			
			return nil
		}
	}
	
	return fmt.Errorf("账号不存在")
}
