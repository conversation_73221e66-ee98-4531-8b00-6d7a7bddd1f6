package handlers

import (
	"database/sql"
	"time"

	"github.com/gin-gonic/gin"
	"steam-manager-web/database"
	"steam-manager-web/middleware"
	"steam-manager-web/models"
	"steam-manager-web/utils"
)

// Register 用户注册
func Register(c *gin.Context) {
	var req models.UserRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 演示模式：如果没有数据库连接，直接返回成功
	if database.DB == nil {
		// 生成演示token
		token, err := utils.GenerateToken(1, req.Username)
		if err != nil {
			utils.InternalServerError(c, "生成token失败")
			return
		}

		utils.Success(c, gin.H{
			"token": token,
			"user": gin.H{
				"id":         1,
				"username":   req.Username,
				"email":      req.Email,
				"is_premium": false,
			},
		})
		return
	}

	// 检查用户名是否已存在
	var count int
	err := database.DB.QueryRow("SELECT COUNT(*) FROM users WHERE username = ?", req.Username).Scan(&count)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	if count > 0 {
		utils.BadRequest(c, "用户名已存在")
		return
	}
	
	// 检查邮箱是否已存在
	err = database.DB.QueryRow("SELECT COUNT(*) FROM users WHERE email = ?", req.Email).Scan(&count)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	if count > 0 {
		utils.BadRequest(c, "邮箱已存在")
		return
	}
	
	// 加密密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		utils.InternalServerError(c, "密码加密失败")
		return
	}
	
	// 创建用户
	result, err := database.DB.Exec(
		"INSERT INTO users (username, email, password, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
		req.Username, req.Email, hashedPassword, time.Now(), time.Now(),
	)
	if err != nil {
		utils.InternalServerError(c, "创建用户失败")
		return
	}
	
	userID, err := result.LastInsertId()
	if err != nil {
		utils.InternalServerError(c, "获取用户ID失败")
		return
	}
	
	// 生成token
	token, err := utils.GenerateToken(int(userID), req.Username)
	if err != nil {
		utils.InternalServerError(c, "生成token失败")
		return
	}
	
	utils.Success(c, gin.H{
		"token": token,
		"user": gin.H{
			"id":         userID,
			"username":   req.Username,
			"email":      req.Email,
			"is_premium": false,
		},
	})
}

// Login 用户登录
func Login(c *gin.Context) {
	var req models.UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 演示模式：如果没有数据库连接，使用演示账号
	if database.DB == nil {
		// 演示账号：admin/admin 或 demo/demo
		if (req.Username == "admin" && req.Password == "admin") ||
		   (req.Username == "demo" && req.Password == "demo") {
			// 生成演示token
			token, err := utils.GenerateToken(1, req.Username)
			if err != nil {
				utils.InternalServerError(c, "生成token失败")
				return
			}

			utils.Success(c, gin.H{
				"token": token,
				"user": gin.H{
					"id":         1,
					"username":   req.Username,
					"email":      req.Username + "@demo.com",
					"is_premium": req.Username == "admin",
				},
			})
			return
		} else {
			utils.BadRequest(c, "演示模式：请使用 admin/admin 或 demo/demo 登录")
			return
		}
	}

	// 查询用户
	var user models.User
	err := database.DB.QueryRow(
		"SELECT id, username, email, password, is_premium, expire_time FROM users WHERE username = ?",
		req.Username,
	).Scan(&user.ID, &user.Username, &user.Email, &user.Password, &user.IsPremium, &user.ExpireTime)
	
	if err == sql.ErrNoRows {
		utils.BadRequest(c, "用户名或密码错误")
		return
	}
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	
	// 验证密码
	if !utils.CheckPassword(req.Password, user.Password) {
		utils.BadRequest(c, "用户名或密码错误")
		return
	}
	
	// 生成token
	token, err := utils.GenerateToken(user.ID, user.Username)
	if err != nil {
		utils.InternalServerError(c, "生成token失败")
		return
	}
	
	utils.Success(c, gin.H{
		"token": token,
		"user":  user.ToResponse(),
	})
}

// GetProfile 获取用户信息
func GetProfile(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	var user models.User
	err := database.DB.QueryRow(
		"SELECT id, username, email, is_premium, expire_time, created_at, updated_at FROM users WHERE id = ?",
		userID,
	).Scan(&user.ID, &user.Username, &user.Email, &user.IsPremium, &user.ExpireTime, &user.CreatedAt, &user.UpdatedAt)
	
	if err == sql.ErrNoRows {
		utils.NotFound(c, "用户不存在")
		return
	}
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	
	utils.Success(c, user.ToResponse())
}

// UpdateProfile 更新用户信息
func UpdateProfile(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	var req struct {
		Email string `json:"email" binding:"required,email"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}
	
	// 检查邮箱是否被其他用户使用
	var count int
	err := database.DB.QueryRow("SELECT COUNT(*) FROM users WHERE email = ? AND id != ?", req.Email, userID).Scan(&count)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	if count > 0 {
		utils.BadRequest(c, "邮箱已被使用")
		return
	}
	
	// 更新用户信息
	_, err = database.DB.Exec("UPDATE users SET email = ?, updated_at = ? WHERE id = ?", req.Email, time.Now(), userID)
	if err != nil {
		utils.InternalServerError(c, "更新用户信息失败")
		return
	}
	
	utils.SuccessWithMessage(c, "更新成功", nil)
}

// ChangePassword 修改密码
func ChangePassword(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	var req struct {
		OldPassword string `json:"old_password" binding:"required"`
		NewPassword string `json:"new_password" binding:"required,min=6"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}
	
	// 查询当前密码
	var currentPassword string
	err := database.DB.QueryRow("SELECT password FROM users WHERE id = ?", userID).Scan(&currentPassword)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	
	// 验证旧密码
	if !utils.CheckPassword(req.OldPassword, currentPassword) {
		utils.BadRequest(c, "原密码错误")
		return
	}
	
	// 加密新密码
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		utils.InternalServerError(c, "密码加密失败")
		return
	}
	
	// 更新密码
	_, err = database.DB.Exec("UPDATE users SET password = ?, updated_at = ? WHERE id = ?", hashedPassword, time.Now(), userID)
	if err != nil {
		utils.InternalServerError(c, "更新密码失败")
		return
	}
	
	utils.SuccessWithMessage(c, "密码修改成功", nil)
}
