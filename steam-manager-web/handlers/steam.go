package handlers

import (
	"strconv"
	
	"github.com/gin-gonic/gin"
	"steam-manager-web/middleware"
	"steam-manager-web/services"
	"steam-manager-web/utils"
)

// LoginSteam Steam登录
func LoginSteam(c *gin.Context) {
	_, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}

	var req struct {
		AccountID int `json:"account_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// TODO: 实现Steam登录逻辑
	// 这里需要调用Steam登录相关的功能
	// 由于涉及到本地Steam客户端操作，在Web版本中可能需要不同的实现方式

	utils.SuccessWithMessage(c, "Steam登录功能在Web版本中暂不支持，请使用桌面版本", nil)
}

// CheckSteamID 检查并获取SteamID
func CheckSteamID(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}

	_, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.BadRequest(c, "无效的账号ID")
		return
	}

	// TODO: 验证账号是否属于当前用户

	// 启动后台任务获取SteamID
	go func() {
		services.BatchGetSteamID(userID)
	}()

	utils.SuccessWithMessage(c, "SteamID检查任务已启动", nil)
}

// CheckBanStatus 检查封禁状态
func CheckBanStatus(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}

	_, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.BadRequest(c, "无效的账号ID")
		return
	}

	// TODO: 验证账号是否属于当前用户

	// 启动后台任务检查封禁状态
	go func() {
		services.BatchCheckBanStatus(userID)
	}()

	utils.SuccessWithMessage(c, "封禁状态检查任务已启动", nil)
}

// BatchCheckSteamID 批量检查SteamID
func BatchCheckSteamID(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	// 启动后台任务
	go func() {
		services.BatchGetSteamID(userID)
	}()
	
	utils.SuccessWithMessage(c, "批量SteamID检查任务已启动", nil)
}

// BatchCheckBanStatus 批量检查封禁状态
func BatchCheckBanStatus(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	// 启动后台任务
	go func() {
		services.BatchCheckBanStatus(userID)
	}()
	
	utils.SuccessWithMessage(c, "批量封禁状态检查任务已启动", nil)
}
