package handlers

import (
	"database/sql"
	"fmt"
	"math"
	"strconv"
	"time"
	
	"github.com/gin-gonic/gin"
	"steam-manager-web/database"
	"steam-manager-web/middleware"
	"steam-manager-web/models"
	"steam-manager-web/utils"
)

// GetAccounts 获取账号列表（分页）
func GetAccounts(c *gin.Context) {
	_, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c<PERSON>("page_size", "10"))
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 演示模式：如果没有数据库连接，返回演示数据
	if database.DB == nil {
		demoAccounts := []*models.SteamAccountResponse{
			{
				ID:       1,
				Username: "demo_account_1",
				SteamID:  "*****************",
				Status:   "正常",
				Notes:    "演示账号1",
			},
			{
				ID:       2,
				Username: "demo_account_2",
				SteamID:  "*****************",
				Status:   "VAC封禁",
				Notes:    "演示账号2",
			},
		}

		response := &models.PaginatedAccountsResponse{
			Accounts:   demoAccounts,
			Total:      2,
			Page:       page,
			PageSize:   pageSize,
			TotalPages: 1,
		}

		utils.Success(c, response)
		return
	}

	userID, _ := middleware.GetUserID(c)

	// 获取总数
	var total int
	err := database.DB.QueryRow("SELECT COUNT(*) FROM steam_accounts WHERE user_id = ?", userID).Scan(&total)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	
	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))
	
	// 获取账号列表
	offset := (page - 1) * pageSize
	rows, err := database.DB.Query(`
		SELECT id, username, steam_id, pubg_ban_status, last_login, last_checked, notes, created_at
		FROM steam_accounts 
		WHERE user_id = ? 
		ORDER BY id ASC 
		LIMIT ? OFFSET ?
	`, userID, pageSize, offset)
	
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	defer rows.Close()
	
	var accounts []*models.SteamAccountResponse
	for rows.Next() {
		var account models.SteamAccount
		var lastLogin, lastChecked sql.NullTime
		
		err := rows.Scan(
			&account.ID,
			&account.Username,
			&account.SteamID,
			&account.PUBGBanStatus,
			&lastLogin,
			&lastChecked,
			&account.Notes,
			&account.CreatedAt,
		)
		if err != nil {
			continue
		}
		
		if lastLogin.Valid {
			account.LastLogin = &lastLogin.Time
		}
		if lastChecked.Valid {
			account.LastChecked = &lastChecked.Time
		}
		
		accounts = append(accounts, account.ToResponse())
	}
	
	if accounts == nil {
		accounts = []*models.SteamAccountResponse{}
	}
	
	response := &models.PaginatedAccountsResponse{
		Accounts:   accounts,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}
	
	utils.Success(c, response)
}

// AddAccount 添加账号
func AddAccount(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	var req models.SteamAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}
	
	// 检查用户名是否已存在
	var count int
	err := database.DB.QueryRow("SELECT COUNT(*) FROM steam_accounts WHERE user_id = ? AND username = ?", userID, req.Username).Scan(&count)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	if count > 0 {
		utils.BadRequest(c, "账号用户名已存在")
		return
	}
	
	// 添加账号
	now := time.Now()
	result, err := database.DB.Exec(`
		INSERT INTO steam_accounts (user_id, username, password, notes, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?)
	`, userID, req.Username, req.Password, req.Notes, now, now)
	
	if err != nil {
		utils.InternalServerError(c, "添加账号失败")
		return
	}
	
	accountID, err := result.LastInsertId()
	if err != nil {
		utils.InternalServerError(c, "获取账号ID失败")
		return
	}
	
	// TODO: 后台获取SteamID和检查封禁状态
	
	utils.SuccessWithMessage(c, "账号添加成功", gin.H{"id": accountID})
}

// UpdateAccount 更新账号
func UpdateAccount(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	accountID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.BadRequest(c, "无效的账号ID")
		return
	}
	
	var req models.SteamAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}
	
	// 检查账号是否属于当前用户
	var count int
	err = database.DB.QueryRow("SELECT COUNT(*) FROM steam_accounts WHERE id = ? AND user_id = ?", accountID, userID).Scan(&count)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	if count == 0 {
		utils.NotFound(c, "账号不存在")
		return
	}
	
	// 检查新用户名是否与其他账号冲突
	err = database.DB.QueryRow("SELECT COUNT(*) FROM steam_accounts WHERE user_id = ? AND username = ? AND id != ?", userID, req.Username, accountID).Scan(&count)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	if count > 0 {
		utils.BadRequest(c, "账号用户名已存在")
		return
	}
	
	// 更新账号
	_, err = database.DB.Exec(`
		UPDATE steam_accounts 
		SET username = ?, password = ?, notes = ?, updated_at = ?
		WHERE id = ? AND user_id = ?
	`, req.Username, req.Password, req.Notes, time.Now(), accountID, userID)
	
	if err != nil {
		utils.InternalServerError(c, "更新账号失败")
		return
	}
	
	utils.SuccessWithMessage(c, "账号更新成功", nil)
}

// DeleteAccount 删除账号
func DeleteAccount(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	accountID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.BadRequest(c, "无效的账号ID")
		return
	}
	
	// 删除账号
	result, err := database.DB.Exec("DELETE FROM steam_accounts WHERE id = ? AND user_id = ?", accountID, userID)
	if err != nil {
		utils.InternalServerError(c, "删除账号失败")
		return
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		utils.InternalServerError(c, "获取影响行数失败")
		return
	}
	
	if rowsAffected == 0 {
		utils.NotFound(c, "账号不存在")
		return
	}
	
	utils.SuccessWithMessage(c, "账号删除成功", nil)
}

// ImportAccounts 批量导入账号
func ImportAccounts(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	var req models.ImportAccountsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}
	
	if len(req.Accounts) == 0 {
		utils.BadRequest(c, "导入账号列表不能为空")
		return
	}
	
	// 开始事务
	tx, err := database.DB.Begin()
	if err != nil {
		utils.InternalServerError(c, "开始事务失败")
		return
	}
	defer tx.Rollback()
	
	successCount := 0
	var errors []string
	
	for i, account := range req.Accounts {
		// 检查用户名是否已存在
		var count int
		err := tx.QueryRow("SELECT COUNT(*) FROM steam_accounts WHERE user_id = ? AND username = ?", userID, account.Username).Scan(&count)
		if err != nil {
			errors = append(errors, fmt.Sprintf("第%d个账号: 数据库查询错误", i+1))
			continue
		}
		if count > 0 {
			errors = append(errors, fmt.Sprintf("第%d个账号: 用户名%s已存在", i+1, account.Username))
			continue
		}
		
		// 添加账号
		now := time.Now()
		_, err = tx.Exec(`
			INSERT INTO steam_accounts (user_id, username, password, notes, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?)
		`, userID, account.Username, account.Password, account.Notes, now, now)
		
		if err != nil {
			errors = append(errors, fmt.Sprintf("第%d个账号: 添加失败", i+1))
			continue
		}
		
		successCount++
	}
	
	// 提交事务
	if err := tx.Commit(); err != nil {
		utils.InternalServerError(c, "提交事务失败")
		return
	}
	
	result := gin.H{
		"success_count": successCount,
		"total_count":   len(req.Accounts),
	}
	
	if len(errors) > 0 {
		result["errors"] = errors
	}
	
	utils.SuccessWithMessage(c, fmt.Sprintf("导入完成，成功%d个，失败%d个", successCount, len(errors)), result)
}

// GetAccountStats 获取账号统计
func GetAccountStats(c *gin.Context) {
	_, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}

	// 演示模式：如果没有数据库连接，返回演示统计
	if database.DB == nil {
		stats := models.AccountStats{
			Total:   2,
			Normal:  1,
			VACBan:  1,
			GameBan: 0,
			Unknown: 0,
		}
		utils.Success(c, stats)
		return
	}

	userID, _ := middleware.GetUserID(c)
	var stats models.AccountStats

	// 获取总数
	err := database.DB.QueryRow("SELECT COUNT(*) FROM steam_accounts WHERE user_id = ?", userID).Scan(&stats.Total)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	
	// 获取各状态数量
	rows, err := database.DB.Query("SELECT pubg_ban_status, COUNT(*) FROM steam_accounts WHERE user_id = ? GROUP BY pubg_ban_status", userID)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	defer rows.Close()
	
	for rows.Next() {
		var status, count int
		if err := rows.Scan(&status, &count); err != nil {
			continue
		}
		
		switch status {
		case -1:
			stats.Unknown = count
		case 0:
			stats.Normal = count
		case 1:
			stats.VACBan = count
		case 2:
			stats.GameBan = count
		}
	}
	
	utils.Success(c, stats)
}
