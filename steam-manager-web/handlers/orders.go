package handlers

import (
	"database/sql"
	"fmt"
	"strconv"
	"time"
	
	"github.com/gin-gonic/gin"
	"steam-manager-web/config"
	"steam-manager-web/database"
	"steam-manager-web/middleware"
	"steam-manager-web/models"
	"steam-manager-web/utils"
)

// CreateOrder 创建订单
func CreateOrder(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	var req models.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}
	
	// 检查是否已有未支付的订单
	var count int
	err := database.DB.QueryRow("SELECT COUNT(*) FROM orders WHERE user_id = ? AND status = 0", userID).Scan(&count)
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	if count > 0 {
		utils.BadRequest(c, "您有未支付的订单，请先完成支付或取消")
		return
	}
	
	// 生成订单号
	orderNo := fmt.Sprintf("SM%d%d", time.Now().Unix(), userID)
	
	// 创建订单
	now := time.Now()
	expireTime := now.Add(30 * time.Minute) // 30分钟过期
	
	result, err := database.DB.Exec(`
		INSERT INTO orders (user_id, order_no, amount, status, pay_method, expire_time, description, created_at, updated_at)
		VALUES (?, ?, ?, 0, ?, ?, ?, ?, ?)
	`, userID, orderNo, config.AppConfig.Payment.YearlyPrice, req.PayMethod, expireTime, "Steam账号管理工具年费会员", now, now)
	
	if err != nil {
		utils.InternalServerError(c, "创建订单失败")
		return
	}
	
	orderID, err := result.LastInsertId()
	if err != nil {
		utils.InternalServerError(c, "获取订单ID失败")
		return
	}
	
	// 查询订单详情
	var order models.Order
	err = database.DB.QueryRow(`
		SELECT id, order_no, amount, status, pay_method, expire_time, description, created_at
		FROM orders WHERE id = ?
	`, orderID).Scan(&order.ID, &order.OrderNo, &order.Amount, &order.Status, &order.PayMethod, &order.ExpireTime, &order.Description, &order.CreatedAt)
	
	if err != nil {
		utils.InternalServerError(c, "查询订单失败")
		return
	}
	
	utils.SuccessWithMessage(c, "订单创建成功", order.ToResponse())
}

// GetOrders 获取订单列表
func GetOrders(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	rows, err := database.DB.Query(`
		SELECT id, order_no, amount, status, pay_method, pay_time, expire_time, description, created_at
		FROM orders 
		WHERE user_id = ? 
		ORDER BY created_at DESC
	`, userID)
	
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	defer rows.Close()
	
	var orders []*models.OrderResponse
	for rows.Next() {
		var order models.Order
		var payTime sql.NullTime
		
		err := rows.Scan(
			&order.ID,
			&order.OrderNo,
			&order.Amount,
			&order.Status,
			&order.PayMethod,
			&payTime,
			&order.ExpireTime,
			&order.Description,
			&order.CreatedAt,
		)
		if err != nil {
			continue
		}
		
		if payTime.Valid {
			order.PayTime = &payTime.Time
		}
		
		orders = append(orders, order.ToResponse())
	}
	
	if orders == nil {
		orders = []*models.OrderResponse{}
	}
	
	utils.Success(c, orders)
}

// GetOrder 获取订单详情
func GetOrder(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	orderID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.BadRequest(c, "无效的订单ID")
		return
	}
	
	var order models.Order
	var payTime sql.NullTime
	err = database.DB.QueryRow(`
		SELECT id, order_no, amount, status, pay_method, pay_time, expire_time, description, created_at
		FROM orders 
		WHERE id = ? AND user_id = ?
	`, orderID, userID).Scan(&order.ID, &order.OrderNo, &order.Amount, &order.Status, &order.PayMethod, &payTime, &order.ExpireTime, &order.Description, &order.CreatedAt)
	
	if err == sql.ErrNoRows {
		utils.NotFound(c, "订单不存在")
		return
	}
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	
	if payTime.Valid {
		order.PayTime = &payTime.Time
	}
	
	utils.Success(c, order.ToResponse())
}

// PayOrder 支付订单（模拟支付）
func PayOrder(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	orderID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.BadRequest(c, "无效的订单ID")
		return
	}
	
	// 开始事务
	tx, err := database.DB.Begin()
	if err != nil {
		utils.InternalServerError(c, "开始事务失败")
		return
	}
	defer tx.Rollback()
	
	// 查询订单
	var order models.Order
	err = tx.QueryRow(`
		SELECT id, user_id, amount, status, expire_time
		FROM orders 
		WHERE id = ? AND user_id = ?
	`, orderID, userID).Scan(&order.ID, &order.UserID, &order.Amount, &order.Status, &order.ExpireTime)
	
	if err == sql.ErrNoRows {
		utils.NotFound(c, "订单不存在")
		return
	}
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	
	// 检查订单状态
	if order.Status != 0 {
		utils.BadRequest(c, "订单状态不正确")
		return
	}
	
	// 检查订单是否过期
	if time.Now().After(order.ExpireTime) {
		utils.BadRequest(c, "订单已过期")
		return
	}
	
	// 更新订单状态
	now := time.Now()
	_, err = tx.Exec("UPDATE orders SET status = 1, pay_time = ?, updated_at = ? WHERE id = ?", now, now, orderID)
	if err != nil {
		utils.InternalServerError(c, "更新订单失败")
		return
	}
	
	// 创建订阅记录
	startTime := now
	endTime := startTime.AddDate(1, 0, 0) // 一年后
	
	_, err = tx.Exec(`
		INSERT INTO subscriptions (user_id, start_time, end_time, is_active, order_id, created_at, updated_at)
		VALUES (?, ?, ?, 1, ?, ?, ?)
	`, userID, startTime, endTime, orderID, now, now)
	
	if err != nil {
		utils.InternalServerError(c, "创建订阅失败")
		return
	}
	
	// 更新用户会员状态
	_, err = tx.Exec("UPDATE users SET is_premium = 1, expire_time = ?, updated_at = ? WHERE id = ?", endTime, now, userID)
	if err != nil {
		utils.InternalServerError(c, "更新用户状态失败")
		return
	}
	
	// 提交事务
	if err := tx.Commit(); err != nil {
		utils.InternalServerError(c, "提交事务失败")
		return
	}
	
	utils.SuccessWithMessage(c, "支付成功，会员已激活", gin.H{
		"expire_time": endTime,
	})
}

// CancelOrder 取消订单
func CancelOrder(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	orderID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.BadRequest(c, "无效的订单ID")
		return
	}
	
	// 更新订单状态
	result, err := database.DB.Exec(`
		UPDATE orders 
		SET status = 2, updated_at = ? 
		WHERE id = ? AND user_id = ? AND status = 0
	`, time.Now(), orderID, userID)
	
	if err != nil {
		utils.InternalServerError(c, "取消订单失败")
		return
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		utils.InternalServerError(c, "获取影响行数失败")
		return
	}
	
	if rowsAffected == 0 {
		utils.BadRequest(c, "订单不存在或状态不正确")
		return
	}
	
	utils.SuccessWithMessage(c, "订单已取消", nil)
}

// GetSubscription 获取订阅信息
func GetSubscription(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.Unauthorized(c, "未登录")
		return
	}
	
	var subscription models.Subscription
	err := database.DB.QueryRow(`
		SELECT id, start_time, end_time, is_active, order_id, created_at
		FROM subscriptions 
		WHERE user_id = ? AND is_active = 1
		ORDER BY created_at DESC 
		LIMIT 1
	`, userID).Scan(&subscription.ID, &subscription.StartTime, &subscription.EndTime, &subscription.IsActive, &subscription.OrderID, &subscription.CreatedAt)
	
	if err == sql.ErrNoRows {
		utils.Success(c, nil) // 没有订阅
		return
	}
	if err != nil {
		utils.InternalServerError(c, "数据库查询错误")
		return
	}
	
	utils.Success(c, subscription.ToResponse())
}
